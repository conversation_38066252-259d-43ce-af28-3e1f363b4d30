#!/usr/bin/env python3
"""
<PERSON>ript to fix orphaned code blocks in main.py by commenting them out
"""

import re

def fix_orphaned_code():
    """Fix orphaned code blocks that are causing indentation errors"""
    
    with open('main.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    fixed_lines = []
    in_class = False
    in_method = False
    method_indent = 0
    
    for i, line in enumerate(lines):
        line_num = i + 1
        stripped = line.strip()
        leading_spaces = len(line) - len(line.lstrip())
        
        # Track if we're in a class
        if stripped.startswith('class '):
            in_class = True
            fixed_lines.append(line)
            continue
        
        # Track if we're in a method
        if re.match(r'\s*(async\s+)?def\s+', line):
            in_method = True
            method_indent = leading_spaces
            fixed_lines.append(line)
            continue
        
        # If we're not in a method but have heavily indented code, it's likely orphaned
        if not in_method and leading_spaces > 16 and stripped:
            # This is likely orphaned code - comment it out
            fixed_lines.append('# ORPHANED CODE: ' + line)
            continue
        
        # If we have a line that starts at column 0 and it's not a class/function/import/comment
        if leading_spaces == 0 and stripped and not any(stripped.startswith(x) for x in [
            'class ', 'def ', 'async def ', 'import ', 'from ', '#', '"""', "'''", 
            'if __name__', 'try:', 'except', 'finally:', 'else:', 'elif'
        ]):
            in_method = False
            in_class = False
        
        # If we encounter an except/finally at wrong indentation, it's orphaned
        if stripped.startswith(('except ', 'finally:')) and not in_method:
            fixed_lines.append('# ORPHANED CODE: ' + line)
            continue
        
        # Normal line
        fixed_lines.append(line)
    
    # Write the fixed file
    with open('main_fixed_orphaned.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print(f"Fixed orphaned code. Output saved to main_fixed_orphaned.py")
    print(f"Processed {len(lines)} lines")

if __name__ == "__main__":
    fix_orphaned_code()
