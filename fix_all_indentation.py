#!/usr/bin/env python3
"""
Comprehensive script to fix all indentation issues in main.py
"""

import re

def fix_all_indentation():
    """Fix all indentation issues by commenting out orphaned code"""
    
    with open('main.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    fixed_lines = []
    in_method = False
    method_indent = 0
    class_indent = 0
    
    for i, line in enumerate(lines):
        line_num = i + 1
        stripped = line.strip()
        leading_spaces = len(line) - len(line.lstrip())
        
        # Skip empty lines
        if not stripped:
            fixed_lines.append(line)
            continue
        
        # Track class definitions
        if stripped.startswith('class '):
            in_method = False
            class_indent = leading_spaces
            fixed_lines.append(line)
            continue
        
        # Track method definitions
        if re.match(r'\s*(async\s+)?def\s+', line):
            in_method = True
            method_indent = leading_spaces
            fixed_lines.append(line)
            continue
        
        # Track end of methods (next method or class at same/lower level)
        if in_method and leading_spaces <= method_indent and stripped and not stripped.startswith('#'):
            if (stripped.startswith('def ') or stripped.startswith('async def ') or 
                stripped.startswith('class ') or stripped.startswith('if __name__') or
                stripped.startswith('import ') or stripped.startswith('from ')):
                in_method = False
        
        # If we're in a method, check for proper indentation
        if in_method:
            expected_min_indent = method_indent + 4
            
            # If the line is heavily over-indented (more than 20 spaces beyond expected), it's likely orphaned
            if leading_spaces > expected_min_indent + 16:
                fixed_lines.append('# ORPHANED CODE: ' + line)
                continue
            
            # If the line has weird indentation that doesn't match Python standards
            if leading_spaces > 0 and leading_spaces < expected_min_indent and not any(stripped.startswith(x) for x in [
                'except', 'finally', 'elif', 'else', 'return', 'raise', 'break', 'continue'
            ]):
                fixed_lines.append('# ORPHANED CODE: ' + line)
                continue
        
        # If we're not in a method and the line is heavily indented, it's orphaned
        elif not in_method and leading_spaces > 8 and stripped:
            # Exception for module-level code that should be indented
            if not any(stripped.startswith(x) for x in [
                'if __name__', 'try:', 'except', 'finally:', 'else:', 'elif',
                'print(', 'logger.', 'sys.exit', 'exit_code', 'asyncio.run'
            ]):
                fixed_lines.append('# ORPHANED CODE: ' + line)
                continue
        
        # Check for orphaned except/finally blocks
        if stripped.startswith(('except ', 'finally:')) and not in_method:
            fixed_lines.append('# ORPHANED CODE: ' + line)
            continue
        
        # Normal line - keep as is
        fixed_lines.append(line)
    
    # Write the fixed file
    with open('main_fixed_all.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print(f"Fixed all indentation issues. Output saved to main_fixed_all.py")
    print(f"Processed {len(lines)} lines")
    
    # Count how many lines were commented out
    orphaned_count = sum(1 for line in fixed_lines if line.strip().startswith('# ORPHANED CODE:'))
    print(f"Commented out {orphaned_count} orphaned code lines")

if __name__ == "__main__":
    fix_all_indentation()
