"""
Advanced TWAP/VWAP Execution Engine
Sophisticated time and volume weighted execution strategies
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timezone, timedelta
import numpy as np
from collections import deque

logger = logging.getLogger(__name__)

class ExecutionAlgorithm(Enum):
    """Advanced execution algorithms"""
    TWAP = "twap"                    # Time-Weighted Average Price
    VWAP = "vwap"                    # Volume-Weighted Average Price
    ADAPTIVE_TWAP = "adaptive_twap"   # Adaptive TWAP with market conditions
    SMART_VWAP = "smart_vwap"        # Smart VWAP with liquidity sensing
    POV = "pov"                      # Participation of Volume
    IS = "implementation_shortfall"   # Implementation Shortfall
    ARRIVAL_PRICE = "arrival_price"   # Arrival Price algorithm

class MarketCondition(Enum):
    """Market condition classifications"""
    CALM = "calm"
    VOLATILE = "volatile"
    TRENDING = "trending"
    ILLIQUID = "illiquid"
    NEWS_DRIVEN = "news_driven"

@dataclass
class ExecutionSlice:
    """Individual execution slice"""
    slice_id: str
    execution_time: datetime
    amount: Decimal
    target_price: Optional[float]
    max_participation: float
    urgency: float
    market_condition: MarketCondition
    expected_impact: float

@dataclass
class ExecutionPlan:
    """Complete execution plan"""
    algorithm: ExecutionAlgorithm
    total_amount: Decimal
    start_time: datetime
    end_time: datetime
    slices: List[ExecutionSlice]
    expected_cost: float
    risk_score: float
    confidence: float

@dataclass
class ExecutionResult:
    """Execution result with performance metrics"""
    plan: ExecutionPlan
    executed_slices: List[Dict[str, Any]]
    total_executed: Decimal
    average_price: float
    total_cost: float
    slippage: float
    market_impact: float
    implementation_shortfall: float
    success_rate: float

class AdvancedTWAPVWAPEngine:
    """Advanced TWAP/VWAP execution engine with intelligent adaptation"""
    
    def __init__(self, exchange_clients: Dict[str, Any], config: Dict = None):
        self.exchange_clients = exchange_clients
        self.config = config or {}
        
        # Execution parameters
        self.default_participation_rate = self.config.get('default_participation_rate', 0.1)  # 10%
        self.max_participation_rate = self.config.get('max_participation_rate', 0.3)  # 30%
        self.min_slice_size = self.config.get('min_slice_size', 0.001)
        self.max_slices = self.config.get('max_slices', 100)
        
        # Market data caches
        self.volume_profiles = {}
        self.price_impact_models = {}
        self.execution_history = deque(maxlen=1000)
        
        # Performance tracking
        self.algorithm_performance = {}
        self.market_condition_performance = {}
        
        logger.info("📊 [TWAP-VWAP] Advanced TWAP/VWAP execution engine initialized")

    async def create_execution_plan(self, symbol: str, side: str, total_amount: Decimal,
                                  algorithm: ExecutionAlgorithm, duration_minutes: int,
                                  market_data: Dict[str, Any]) -> ExecutionPlan:
        """Create optimized execution plan"""
        try:
            start_time = datetime.now(timezone.utc)
            end_time = start_time + timedelta(minutes=duration_minutes)
            
            # Analyze market conditions
            market_condition = await self._analyze_market_condition(symbol, market_data)
            
            # Get volume profile if needed
            volume_profile = None
            if algorithm in [ExecutionAlgorithm.VWAP, ExecutionAlgorithm.SMART_VWAP, ExecutionAlgorithm.POV]:
                volume_profile = await self._get_volume_profile(symbol)
            
            # Create execution slices based on algorithm
            slices = await self._create_execution_slices(
                symbol, side, total_amount, algorithm, start_time, end_time,
                market_condition, volume_profile, market_data
            )
            
            # Calculate expected costs and risks
            expected_cost = await self._estimate_execution_cost(symbol, slices, market_data)
            risk_score = await self._calculate_execution_risk(slices, market_condition)
            confidence = await self._calculate_execution_confidence(algorithm, market_condition)
            
            return ExecutionPlan(
                algorithm=algorithm,
                total_amount=total_amount,
                start_time=start_time,
                end_time=end_time,
                slices=slices,
                expected_cost=expected_cost,
                risk_score=risk_score,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"❌ [TWAP-VWAP] Error creating execution plan: {e}")
            return self._create_fallback_plan(symbol, side, total_amount, duration_minutes)

    async def execute_plan(self, plan: ExecutionPlan, symbol: str, side: str) -> ExecutionResult:
        """Execute the trading plan"""
        try:
            logger.info(f"📊 [TWAP-VWAP] Executing {plan.algorithm.value} plan: "
                       f"{len(plan.slices)} slices over {(plan.end_time - plan.start_time).total_seconds()/60:.1f}m")
            
            executed_slices = []
            total_executed = Decimal('0')
            total_cost = 0.0
            
            for slice_info in plan.slices:
                try:
                    # Wait until slice execution time
                    current_time = datetime.now(timezone.utc)
                    if slice_info.execution_time > current_time:
                        wait_seconds = (slice_info.execution_time - current_time).total_seconds()
                        if wait_seconds > 0:
                            await asyncio.sleep(min(wait_seconds, 300))  # Max 5 minutes wait
                    
                    # Execute slice
                    slice_result = await self._execute_slice(symbol, side, slice_info)
                    
                    if slice_result.get('success'):
                        executed_slices.append(slice_result)
                        total_executed += slice_result.get('executed_amount', Decimal('0'))
                        total_cost += slice_result.get('cost', 0.0)
                        
                        logger.info(f"📊 [SLICE] Executed {slice_result.get('executed_amount', 0)} at {slice_result.get('price', 0)}")
                    else:
                        logger.warning(f"⚠️ [SLICE] Failed to execute slice: {slice_result.get('error')}")
                
                except Exception as slice_error:
                    logger.error(f"❌ [SLICE] Error executing slice: {slice_error}")
                    continue
            
            # Calculate performance metrics
            performance_metrics = await self._calculate_performance_metrics(
                plan, executed_slices, total_executed, total_cost
            )
            
            # Store execution history for learning
            self._store_execution_history(plan, performance_metrics)
            
            return ExecutionResult(
                plan=plan,
                executed_slices=executed_slices,
                total_executed=total_executed,
                average_price=performance_metrics['average_price'],
                total_cost=total_cost,
                slippage=performance_metrics['slippage'],
                market_impact=performance_metrics['market_impact'],
                implementation_shortfall=performance_metrics['implementation_shortfall'],
                success_rate=performance_metrics['success_rate']
            )
            
        except Exception as e:
            logger.error(f"❌ [TWAP-VWAP] Error executing plan: {e}")
            return self._create_fallback_result(plan)

    async def _analyze_market_condition(self, symbol: str, market_data: Dict[str, Any]) -> MarketCondition:
        """Analyze current market conditions"""
        try:
            volatility = market_data.get('volatility', 0.02)
            volume = market_data.get('volume', 0)
            price_change = market_data.get('price_change_1h', 0)
            spread = market_data.get('spread', 0.001)
            
            # High volatility
            if volatility > 0.05:
                return MarketCondition.VOLATILE
            
            # Strong trend
            elif abs(price_change) > 0.03:
                return MarketCondition.TRENDING
            
            # Low liquidity
            elif volume < 100000 or spread > 0.005:
                return MarketCondition.ILLIQUID
            
            # News-driven (high volume + high volatility)
            elif volume > 1000000 and volatility > 0.03:
                return MarketCondition.NEWS_DRIVEN
            
            # Default to calm
            else:
                return MarketCondition.CALM
                
        except Exception as e:
            logger.error(f"❌ [TWAP-VWAP] Error analyzing market condition: {e}")
            return MarketCondition.CALM

    async def _get_volume_profile(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get historical volume profile for the symbol"""
        try:
            # Check cache first
            if symbol in self.volume_profiles:
                cache_time = self.volume_profiles[symbol].get('timestamp', 0)
                if time.time() - cache_time < 300:  # 5 minutes cache
                    return self.volume_profiles[symbol]['data']
            
            # Get volume data from exchange
            volume_data = await self._fetch_volume_data(symbol)
            
            if volume_data:
                # Process volume profile
                profile = self._process_volume_profile(volume_data)
                
                # Cache the result
                self.volume_profiles[symbol] = {
                    'data': profile,
                    'timestamp': time.time()
                }
                
                return profile
            
            return None
            
        except Exception as e:
            logger.error(f"❌ [TWAP-VWAP] Error getting volume profile: {e}")
            return None

    async def _create_execution_slices(self, symbol: str, side: str, total_amount: Decimal,
                                     algorithm: ExecutionAlgorithm, start_time: datetime,
                                     end_time: datetime, market_condition: MarketCondition,
                                     volume_profile: Optional[Dict], market_data: Dict) -> List[ExecutionSlice]:
        """Create execution slices based on algorithm"""
        try:
            duration_seconds = (end_time - start_time).total_seconds()
            
            if algorithm == ExecutionAlgorithm.TWAP:
                return await self._create_twap_slices(
                    total_amount, start_time, duration_seconds, market_condition
                )
            elif algorithm == ExecutionAlgorithm.ADAPTIVE_TWAP:
                return await self._create_adaptive_twap_slices(
                    total_amount, start_time, duration_seconds, market_condition, market_data
                )
            elif algorithm == ExecutionAlgorithm.VWAP:
                return await self._create_vwap_slices(
                    total_amount, start_time, duration_seconds, volume_profile, market_condition
                )
            elif algorithm == ExecutionAlgorithm.SMART_VWAP:
                return await self._create_smart_vwap_slices(
                    total_amount, start_time, duration_seconds, volume_profile, market_condition, market_data
                )
            elif algorithm == ExecutionAlgorithm.POV:
                return await self._create_pov_slices(
                    total_amount, start_time, duration_seconds, volume_profile, market_condition
                )
            else:
                # Fallback to simple TWAP
                return await self._create_twap_slices(
                    total_amount, start_time, duration_seconds, market_condition
                )
                
        except Exception as e:
            logger.error(f"❌ [TWAP-VWAP] Error creating execution slices: {e}")
            return self._create_fallback_slices(total_amount, start_time, end_time)

    async def _create_twap_slices(self, total_amount: Decimal, start_time: datetime,
                                duration_seconds: float, market_condition: MarketCondition) -> List[ExecutionSlice]:
        """Create TWAP execution slices"""
        try:
            # Determine number of slices based on market condition
            if market_condition == MarketCondition.VOLATILE:
                num_slices = min(self.max_slices, max(10, int(duration_seconds / 30)))  # 30s intervals
            elif market_condition == MarketCondition.ILLIQUID:
                num_slices = min(self.max_slices, max(5, int(duration_seconds / 120)))  # 2min intervals
            else:
                num_slices = min(self.max_slices, max(6, int(duration_seconds / 60)))   # 1min intervals
            
            slice_amount = total_amount / num_slices
            slice_interval = duration_seconds / num_slices
            
            slices = []
            for i in range(num_slices):
                execution_time = start_time + timedelta(seconds=i * slice_interval)
                
                # Adjust urgency based on position in sequence
                urgency = 0.3 + (i / num_slices) * 0.4  # Increase urgency over time
                
                slice_info = ExecutionSlice(
                    slice_id=f"twap_{i+1}_{int(time.time())}",
                    execution_time=execution_time,
                    amount=slice_amount,
                    target_price=None,  # Market price
                    max_participation=self.default_participation_rate,
                    urgency=urgency,
                    market_condition=market_condition,
                    expected_impact=0.001  # 0.1% expected impact
                )
                slices.append(slice_info)
            
            return slices
            
        except Exception as e:
            logger.error(f"❌ [TWAP-VWAP] Error creating TWAP slices: {e}")
            return []

    async def _create_vwap_slices(self, total_amount: Decimal, start_time: datetime,
                                duration_seconds: float, volume_profile: Optional[Dict],
                                market_condition: MarketCondition) -> List[ExecutionSlice]:
        """Create VWAP execution slices"""
        try:
            if not volume_profile:
                # Fallback to TWAP if no volume profile
                return await self._create_twap_slices(total_amount, start_time, duration_seconds, market_condition)
            
            # Get volume distribution
            volume_distribution = volume_profile.get('hourly_distribution', {})
            
            # Create slices based on volume distribution
            slices = []
            current_time = start_time
            remaining_amount = total_amount
            
            # Calculate time intervals (e.g., 15-minute intervals)
            interval_minutes = max(5, min(30, int(duration_seconds / 60 / 8)))  # 8 intervals max
            
            while current_time < start_time + timedelta(seconds=duration_seconds) and remaining_amount > 0:
                # Get expected volume for this time period
                hour = current_time.hour
                volume_weight = volume_distribution.get(str(hour), 1.0)
                
                # Calculate slice amount based on volume weight
                slice_amount = min(remaining_amount, total_amount * volume_weight * 0.2)  # Max 20% per slice
                
                if slice_amount >= self.min_slice_size:
                    slice_info = ExecutionSlice(
                        slice_id=f"vwap_{len(slices)+1}_{int(time.time())}",
                        execution_time=current_time,
                        amount=slice_amount,
                        target_price=None,
                        max_participation=min(self.max_participation_rate, volume_weight * 0.3),
                        urgency=0.4,
                        market_condition=market_condition,
                        expected_impact=0.0005 * volume_weight  # Lower impact with higher volume
                    )
                    slices.append(slice_info)
                    remaining_amount -= slice_amount
                
                current_time += timedelta(minutes=interval_minutes)
            
            # If there's remaining amount, add it to the last slice or create a new one
            if remaining_amount > 0:
                if slices:
                    slices[-1].amount += remaining_amount
                else:
                    # Create a single slice for remaining amount
                    slice_info = ExecutionSlice(
                        slice_id=f"vwap_final_{int(time.time())}",
                        execution_time=start_time,
                        amount=remaining_amount,
                        target_price=None,
                        max_participation=self.default_participation_rate,
                        urgency=0.5,
                        market_condition=market_condition,
                        expected_impact=0.001
                    )
                    slices.append(slice_info)
            
            return slices
            
        except Exception as e:
            logger.error(f"❌ [TWAP-VWAP] Error creating VWAP slices: {e}")
            return []

    async def _execute_slice(self, symbol: str, side: str, slice_info: ExecutionSlice) -> Dict[str, Any]:
        """Execute a single slice"""
        try:
            # Get the primary exchange client
            primary_client = list(self.exchange_clients.values())[0] if self.exchange_clients else None
            
            if not primary_client:
                return {'success': False, 'error': 'No exchange client available'}
            
            # Execute the order
            if hasattr(primary_client, 'place_order'):
                order_result = await primary_client.place_order(
                    symbol=symbol,
                    side=side.lower(),
                    amount=float(slice_info.amount),
                    order_type='Market'
                )
                
                if order_result and order_result.get('order_id'):
                    return {
                        'success': True,
                        'slice_id': slice_info.slice_id,
                        'order_id': order_result['order_id'],
                        'executed_amount': slice_info.amount,
                        'price': order_result.get('price', 0),
                        'cost': float(slice_info.amount) * order_result.get('price', 0),
                        'execution_time': datetime.now(timezone.utc),
                        'market_condition': slice_info.market_condition.value
                    }
            
            return {'success': False, 'error': 'Order execution failed'}
            
        except Exception as e:
            logger.error(f"❌ [SLICE] Error executing slice {slice_info.slice_id}: {e}")
            return {'success': False, 'error': str(e)}

    def _create_fallback_plan(self, symbol: str, side: str, total_amount: Decimal, 
                            duration_minutes: int) -> ExecutionPlan:
        """Create fallback execution plan"""
        start_time = datetime.now(timezone.utc)
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        # Single slice execution
        slice_info = ExecutionSlice(
            slice_id=f"fallback_{int(time.time())}",
            execution_time=start_time,
            amount=total_amount,
            target_price=None,
            max_participation=self.default_participation_rate,
            urgency=0.5,
            market_condition=MarketCondition.CALM,
            expected_impact=0.001
        )
        
        return ExecutionPlan(
            algorithm=ExecutionAlgorithm.TWAP,
            total_amount=total_amount,
            start_time=start_time,
            end_time=end_time,
            slices=[slice_info],
            expected_cost=0.001,
            risk_score=0.5,
            confidence=0.5
        )

# Export the main classes
__all__ = ['AdvancedTWAPVWAPEngine', 'ExecutionPlan', 'ExecutionResult', 'ExecutionAlgorithm', 'MarketCondition']
