#!/usr/bin/env python3
"""
Script to remove the orphaned section entirely
"""

def remove_orphaned_section():
    """Remove the orphaned section from line 3719 to 3793"""
    
    with open('main.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Remove lines 3719 to 3793 (0-based indexing: 3718 to 3792)
    start_line = 3718  # 0-based
    end_line = 3792    # 0-based
    
    # Keep lines before the orphaned section
    fixed_lines = lines[:start_line]
    
    # Add a comment about the removed section
    fixed_lines.append('        # ORPHANED CODE SECTION REMOVED (lines 3719-3793)\n')
    fixed_lines.append('        # This section contained orphaned code with indentation issues\n')
    fixed_lines.append('        # The functionality will be reimplemented properly later\n')
    fixed_lines.append('\n')
    
    # Keep lines after the orphaned section
    if end_line + 1 < len(lines):
        fixed_lines.extend(lines[end_line + 1:])
    
    # Write the fixed file
    with open('main_cleaned.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print(f"Removed orphaned section from line 3719 to 3793")
    print(f"Output saved to main_cleaned.py")
    print(f"Original file had {len(lines)} lines")
    print(f"Cleaned file has {len(fixed_lines)} lines")

if __name__ == "__main__":
    remove_orphaned_section()
