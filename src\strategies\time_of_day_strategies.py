"""
Time-of-Day Specific Trading Strategies
Specialized strategies optimized for different market sessions and time periods
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timezone, timedelta
import numpy as np

logger = logging.getLogger(__name__)

class TradingSession(Enum):
    """Global trading sessions"""
    SYDNEY = "sydney"           # 22:00-07:00 UTC
    TOKYO = "tokyo"             # 00:00-09:00 UTC
    LONDON = "london"           # 08:00-17:00 UTC
    NEW_YORK = "new_york"       # 13:00-22:00 UTC
    OVERLAP_LONDON_NY = "london_ny_overlap"  # 13:00-17:00 UTC
    OVERLAP_TOKYO_LONDON = "tokyo_london_overlap"  # 08:00-09:00 UTC

class TimeStrategy(Enum):
    """Time-specific strategy types"""
    BREAKOUT_LONDON_OPEN = "breakout_london_open"
    REVERSAL_NY_CLOSE = "reversal_ny_close"
    MOMENTUM_OVERLAP = "momentum_overlap"
    RANGE_ASIAN_SESSION = "range_asian_session"
    NEWS_ANTICIPATION = "news_anticipation"
    WEEKEND_POSITIONING = "weekend_positioning"
    MONTH_END_REBALANCING = "month_end_rebalancing"

@dataclass
class SessionCharacteristics:
    """Characteristics of a trading session"""
    session: TradingSession
    start_hour: int
    end_hour: int
    avg_volatility: float
    avg_volume: float
    typical_spread: float
    dominant_currencies: List[str]
    key_events: List[str]
    optimal_strategies: List[TimeStrategy]

@dataclass
class TimeBasedSignal:
    """Time-based trading signal"""
    strategy: TimeStrategy
    session: TradingSession
    symbol: str
    direction: str  # 'BUY', 'SELL', 'HOLD'
    strength: float  # 0.0 to 1.0
    confidence: float  # 0.0 to 1.0
    time_window: Tuple[datetime, datetime]
    expected_duration: timedelta
    risk_level: str
    reasoning: str

class TimeOfDayStrategies:
    """Time-of-day specific trading strategies"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        
        # Session definitions
        self.session_characteristics = {
            TradingSession.SYDNEY: SessionCharacteristics(
                session=TradingSession.SYDNEY,
                start_hour=22, end_hour=7,
                avg_volatility=0.015,
                avg_volume=0.3,
                typical_spread=0.002,
                dominant_currencies=['AUD', 'NZD', 'JPY'],
                key_events=['RBA announcements', 'Australian data'],
                optimal_strategies=[TimeStrategy.RANGE_ASIAN_SESSION]
            ),
            TradingSession.TOKYO: SessionCharacteristics(
                session=TradingSession.TOKYO,
                start_hour=0, end_hour=9,
                avg_volatility=0.018,
                avg_volume=0.5,
                typical_spread=0.0015,
                dominant_currencies=['JPY', 'AUD', 'NZD'],
                key_events=['BOJ announcements', 'Japanese data'],
                optimal_strategies=[TimeStrategy.RANGE_ASIAN_SESSION, TimeStrategy.NEWS_ANTICIPATION]
            ),
            TradingSession.LONDON: SessionCharacteristics(
                session=TradingSession.LONDON,
                start_hour=8, end_hour=17,
                avg_volatility=0.025,
                avg_volume=0.8,
                typical_spread=0.001,
                dominant_currencies=['EUR', 'GBP', 'CHF'],
                key_events=['ECB announcements', 'UK data', 'European data'],
                optimal_strategies=[TimeStrategy.BREAKOUT_LONDON_OPEN, TimeStrategy.MOMENTUM_OVERLAP]
            ),
            TradingSession.NEW_YORK: SessionCharacteristics(
                session=TradingSession.NEW_YORK,
                start_hour=13, end_hour=22,
                avg_volatility=0.022,
                avg_volume=0.9,
                typical_spread=0.0008,
                dominant_currencies=['USD', 'CAD'],
                key_events=['Fed announcements', 'US data', 'FOMC'],
                optimal_strategies=[TimeStrategy.MOMENTUM_OVERLAP, TimeStrategy.REVERSAL_NY_CLOSE]
            )
        }
        
        # Strategy performance tracking
        self.strategy_performance = {}
        self.session_performance = {}
        
        # Time-based parameters
        self.volatility_thresholds = {
            'low': 0.01,
            'medium': 0.025,
            'high': 0.04
        }
        
        logger.info("⏰ [TIME-STRATEGIES] Time-of-day strategies initialized")

    async def analyze_time_based_opportunities(self, market_data: Dict[str, Any],
                                             available_symbols: List[str]) -> List[TimeBasedSignal]:
        """Analyze time-based trading opportunities"""
        try:
            current_time = datetime.now(timezone.utc)
            signals = []
            
            # Determine current session
            current_session = self._get_current_session(current_time)
            session_progress = self._get_session_progress(current_time, current_session)
            
            logger.info(f"⏰ [TIME-ANALYSIS] Current session: {current_session.value}, progress: {session_progress:.1%}")
            
            # Analyze each available symbol
            for symbol in available_symbols:
                symbol_signals = await self._analyze_symbol_time_opportunities(
                    symbol, current_time, current_session, session_progress, market_data
                )
                signals.extend(symbol_signals)
            
            # Filter and rank signals
            filtered_signals = self._filter_signals_by_time_criteria(signals, current_time)
            ranked_signals = self._rank_signals_by_time_strength(filtered_signals)
            
            logger.info(f"⏰ [TIME-ANALYSIS] Generated {len(ranked_signals)} time-based signals")
            
            return ranked_signals
            
        except Exception as e:
            logger.error(f"❌ [TIME-STRATEGIES] Error analyzing time opportunities: {e}")
            return []

    def _get_current_session(self, current_time: datetime) -> TradingSession:
        """Determine current trading session"""
        try:
            hour = current_time.hour
            
            # Check for overlaps first (higher priority)
            if 13 <= hour < 17:  # London-NY overlap
                return TradingSession.OVERLAP_LONDON_NY
            elif 8 <= hour < 9:   # Tokyo-London overlap
                return TradingSession.OVERLAP_TOKYO_LONDON
            
            # Check individual sessions
            elif 8 <= hour < 17:  # London
                return TradingSession.LONDON
            elif 13 <= hour < 22: # New York
                return TradingSession.NEW_YORK
            elif 0 <= hour < 9:   # Tokyo
                return TradingSession.TOKYO
            else:                 # Sydney/Asian
                return TradingSession.SYDNEY
                
        except Exception as e:
            logger.error(f"❌ [TIME-STRATEGIES] Error determining session: {e}")
            return TradingSession.NEW_YORK

    def _get_session_progress(self, current_time: datetime, session: TradingSession) -> float:
        """Calculate progress through current session (0.0 to 1.0)"""
        try:
            if session not in self.session_characteristics:
                return 0.5
            
            char = self.session_characteristics[session]
            hour = current_time.hour
            minute = current_time.minute
            
            current_decimal_hour = hour + minute / 60.0
            
            # Handle sessions that cross midnight
            if char.start_hour > char.end_hour:
                if hour >= char.start_hour:
                    session_hour = hour - char.start_hour
                else:
                    session_hour = (24 - char.start_hour) + hour
                session_duration = (24 - char.start_hour) + char.end_hour
            else:
                session_hour = current_decimal_hour - char.start_hour
                session_duration = char.end_hour - char.start_hour
            
            progress = session_hour / session_duration if session_duration > 0 else 0.5
            return max(0.0, min(1.0, progress))
            
        except Exception as e:
            logger.error(f"❌ [TIME-STRATEGIES] Error calculating session progress: {e}")
            return 0.5

    async def _analyze_symbol_time_opportunities(self, symbol: str, current_time: datetime,
                                               session: TradingSession, session_progress: float,
                                               market_data: Dict[str, Any]) -> List[TimeBasedSignal]:
        """Analyze time-based opportunities for a specific symbol"""
        try:
            signals = []
            
            # Get symbol-specific market data
            symbol_data = market_data.get(symbol, {})
            
            # Strategy 1: London Open Breakout
            if session == TradingSession.LONDON and 0.0 <= session_progress <= 0.3:
                breakout_signal = await self._analyze_london_open_breakout(
                    symbol, current_time, symbol_data
                )
                if breakout_signal:
                    signals.append(breakout_signal)
            
            # Strategy 2: NY-London Overlap Momentum
            elif session == TradingSession.OVERLAP_LONDON_NY:
                momentum_signal = await self._analyze_overlap_momentum(
                    symbol, current_time, symbol_data
                )
                if momentum_signal:
                    signals.append(momentum_signal)
            
            # Strategy 3: Asian Session Range Trading
            elif session in [TradingSession.TOKYO, TradingSession.SYDNEY]:
                range_signal = await self._analyze_asian_range_trading(
                    symbol, current_time, symbol_data
                )
                if range_signal:
                    signals.append(range_signal)
            
            # Strategy 4: NY Close Reversal
            elif session == TradingSession.NEW_YORK and session_progress >= 0.8:
                reversal_signal = await self._analyze_ny_close_reversal(
                    symbol, current_time, symbol_data
                )
                if reversal_signal:
                    signals.append(reversal_signal)
            
            # Strategy 5: Weekend Positioning
            if current_time.weekday() == 4 and current_time.hour >= 20:  # Friday evening
                weekend_signal = await self._analyze_weekend_positioning(
                    symbol, current_time, symbol_data
                )
                if weekend_signal:
                    signals.append(weekend_signal)
            
            return signals
            
        except Exception as e:
            logger.error(f"❌ [TIME-STRATEGIES] Error analyzing {symbol} opportunities: {e}")
            return []

    async def _analyze_london_open_breakout(self, symbol: str, current_time: datetime,
                                          symbol_data: Dict[str, Any]) -> Optional[TimeBasedSignal]:
        """Analyze London open breakout opportunity"""
        try:
            # Check for overnight range and potential breakout
            volatility = symbol_data.get('volatility', 0.02)
            volume = symbol_data.get('volume', 0)
            price_change = symbol_data.get('price_change_1h', 0)
            
            # Conditions for London open breakout
            if (volatility > 0.015 and  # Sufficient volatility
                volume > 500000 and     # Good volume
                abs(price_change) > 0.01):  # Price movement
                
                direction = 'BUY' if price_change > 0 else 'SELL'
                strength = min(1.0, abs(price_change) * 50)  # Scale price change
                confidence = min(1.0, (volatility * volume) / 10000000)
                
                return TimeBasedSignal(
                    strategy=TimeStrategy.BREAKOUT_LONDON_OPEN,
                    session=TradingSession.LONDON,
                    symbol=symbol,
                    direction=direction,
                    strength=strength,
                    confidence=confidence,
                    time_window=(current_time, current_time + timedelta(hours=2)),
                    expected_duration=timedelta(hours=1),
                    risk_level='medium',
                    reasoning=f'London open breakout: {price_change:.2%} move with {volatility:.2%} volatility'
                )
            
            return None
            
        except Exception as e:
            logger.error(f"❌ [TIME-STRATEGIES] Error analyzing London breakout: {e}")
            return None

    async def _analyze_overlap_momentum(self, symbol: str, current_time: datetime,
                                      symbol_data: Dict[str, Any]) -> Optional[TimeBasedSignal]:
        """Analyze momentum during London-NY overlap"""
        try:
            # High liquidity period - momentum strategies work well
            volatility = symbol_data.get('volatility', 0.02)
            volume = symbol_data.get('volume', 0)
            price_change = symbol_data.get('price_change_4h', 0)
            
            # Conditions for momentum trading
            if (volatility > 0.02 and      # Good volatility
                volume > 1000000 and       # High volume
                abs(price_change) > 0.015): # Strong momentum
                
                direction = 'BUY' if price_change > 0 else 'SELL'
                strength = min(1.0, abs(price_change) * 30)
                confidence = min(1.0, (volatility * volume) / 20000000)
                
                return TimeBasedSignal(
                    strategy=TimeStrategy.MOMENTUM_OVERLAP,
                    session=TradingSession.OVERLAP_LONDON_NY,
                    symbol=symbol,
                    direction=direction,
                    strength=strength,
                    confidence=confidence,
                    time_window=(current_time, current_time + timedelta(hours=3)),
                    expected_duration=timedelta(hours=2),
                    risk_level='medium',
                    reasoning=f'Overlap momentum: {price_change:.2%} trend with high liquidity'
                )
            
            return None
            
        except Exception as e:
            logger.error(f"❌ [TIME-STRATEGIES] Error analyzing overlap momentum: {e}")
            return None

    async def _analyze_asian_range_trading(self, symbol: str, current_time: datetime,
                                         symbol_data: Dict[str, Any]) -> Optional[TimeBasedSignal]:
        """Analyze range trading during Asian session"""
        try:
            # Lower volatility period - range strategies work well
            volatility = symbol_data.get('volatility', 0.02)
            volume = symbol_data.get('volume', 0)
            price_change = symbol_data.get('price_change_1h', 0)
            
            # Conditions for range trading (low volatility, mean reversion)
            if (volatility < 0.02 and      # Low volatility
                volume > 100000 and        # Minimum volume
                abs(price_change) < 0.01): # Limited price movement
                
                # Look for mean reversion opportunities
                direction = 'SELL' if price_change > 0.005 else 'BUY' if price_change < -0.005 else 'HOLD'
                
                if direction != 'HOLD':
                    strength = min(1.0, abs(price_change) * 100)  # Higher sensitivity for small moves
                    confidence = min(1.0, (1 - volatility) * 2)   # Higher confidence with lower volatility
                    
                    return TimeBasedSignal(
                        strategy=TimeStrategy.RANGE_ASIAN_SESSION,
                        session=TradingSession.TOKYO,
                        symbol=symbol,
                        direction=direction,
                        strength=strength,
                        confidence=confidence,
                        time_window=(current_time, current_time + timedelta(hours=4)),
                        expected_duration=timedelta(hours=2),
                        risk_level='low',
                        reasoning=f'Asian range trading: {price_change:.2%} move in low volatility environment'
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"❌ [TIME-STRATEGIES] Error analyzing Asian range: {e}")
            return None

    async def _analyze_ny_close_reversal(self, symbol: str, current_time: datetime,
                                       symbol_data: Dict[str, Any]) -> Optional[TimeBasedSignal]:
        """Analyze reversal opportunities near NY close"""
        try:
            # End of day - look for reversal patterns
            volatility = symbol_data.get('volatility', 0.02)
            price_change = symbol_data.get('price_change_8h', 0)
            volume = symbol_data.get('volume', 0)
            
            # Conditions for end-of-day reversal
            if (abs(price_change) > 0.02 and  # Significant move during the day
                volatility > 0.015 and         # Sufficient volatility
                volume > 500000):              # Good volume
                
                # Reversal direction (opposite to day's move)
                direction = 'SELL' if price_change > 0 else 'BUY'
                strength = min(1.0, abs(price_change) * 25)
                confidence = min(1.0, volatility * 20)
                
                return TimeBasedSignal(
                    strategy=TimeStrategy.REVERSAL_NY_CLOSE,
                    session=TradingSession.NEW_YORK,
                    symbol=symbol,
                    direction=direction,
                    strength=strength,
                    confidence=confidence,
                    time_window=(current_time, current_time + timedelta(hours=1)),
                    expected_duration=timedelta(minutes=30),
                    risk_level='high',
                    reasoning=f'NY close reversal: {price_change:.2%} day move suggests reversal'
                )
            
            return None
            
        except Exception as e:
            logger.error(f"❌ [TIME-STRATEGIES] Error analyzing NY reversal: {e}")
            return None

    async def _analyze_weekend_positioning(self, symbol: str, current_time: datetime,
                                         symbol_data: Dict[str, Any]) -> Optional[TimeBasedSignal]:
        """Analyze weekend positioning strategy"""
        try:
            # Friday evening - position for weekend gaps
            volatility = symbol_data.get('volatility', 0.02)
            price_change = symbol_data.get('price_change_24h', 0)
            
            # Conservative positioning before weekend
            if volatility < 0.03:  # Lower risk during weekends
                # Slight bias toward reducing risk
                direction = 'SELL' if abs(price_change) > 0.01 else 'HOLD'
                
                if direction != 'HOLD':
                    strength = 0.3  # Conservative strength
                    confidence = 0.6  # Moderate confidence
                    
                    return TimeBasedSignal(
                        strategy=TimeStrategy.WEEKEND_POSITIONING,
                        session=TradingSession.NEW_YORK,
                        symbol=symbol,
                        direction=direction,
                        strength=strength,
                        confidence=confidence,
                        time_window=(current_time, current_time + timedelta(hours=2)),
                        expected_duration=timedelta(hours=1),
                        risk_level='low',
                        reasoning='Weekend positioning: reducing risk before market close'
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"❌ [TIME-STRATEGIES] Error analyzing weekend positioning: {e}")
            return None

    def _filter_signals_by_time_criteria(self, signals: List[TimeBasedSignal], 
                                       current_time: datetime) -> List[TimeBasedSignal]:
        """Filter signals based on time criteria"""
        try:
            filtered = []
            
            for signal in signals:
                # Check if signal is within valid time window
                start_time, end_time = signal.time_window
                if start_time <= current_time <= end_time:
                    # Check minimum strength and confidence
                    if signal.strength >= 0.3 and signal.confidence >= 0.4:
                        filtered.append(signal)
            
            return filtered
            
        except Exception as e:
            logger.error(f"❌ [TIME-STRATEGIES] Error filtering signals: {e}")
            return signals

    def _rank_signals_by_time_strength(self, signals: List[TimeBasedSignal]) -> List[TimeBasedSignal]:
        """Rank signals by time-adjusted strength"""
        try:
            # Calculate composite score for each signal
            scored_signals = []
            
            for signal in signals:
                # Composite score: strength * confidence * time_factor
                time_factor = self._calculate_time_factor(signal)
                composite_score = signal.strength * signal.confidence * time_factor
                
                scored_signals.append((composite_score, signal))
            
            # Sort by score (highest first)
            scored_signals.sort(key=lambda x: x[0], reverse=True)
            
            return [signal for score, signal in scored_signals]
            
        except Exception as e:
            logger.error(f"❌ [TIME-STRATEGIES] Error ranking signals: {e}")
            return signals

    def _calculate_time_factor(self, signal: TimeBasedSignal) -> float:
        """Calculate time factor for signal strength"""
        try:
            # Time factors based on strategy and session
            strategy_factors = {
                TimeStrategy.BREAKOUT_LONDON_OPEN: 1.2,
                TimeStrategy.MOMENTUM_OVERLAP: 1.3,
                TimeStrategy.RANGE_ASIAN_SESSION: 0.9,
                TimeStrategy.REVERSAL_NY_CLOSE: 1.1,
                TimeStrategy.WEEKEND_POSITIONING: 0.8
            }
            
            return strategy_factors.get(signal.strategy, 1.0)
            
        except Exception as e:
            logger.error(f"❌ [TIME-STRATEGIES] Error calculating time factor: {e}")
            return 1.0

# Export the main class
__all__ = ['TimeOfDayStrategies', 'TimeBasedSignal', 'TradingSession', 'TimeStrategy']
