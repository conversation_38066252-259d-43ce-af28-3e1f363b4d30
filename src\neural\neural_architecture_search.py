"""
Neural Architecture Search for Automated Model Optimization
Automatically discovers optimal neural network architectures for trading
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
import random
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, field
from collections import defaultdict
import json

logger = logging.getLogger(__name__)

@dataclass
class SearchSpace:
    """Defines the search space for neural architecture search"""
    layer_types: List[str] = field(default_factory=lambda: ['linear', 'lstm', 'gru', 'attention', 'conv1d'])
    hidden_sizes: List[int] = field(default_factory=lambda: [32, 64, 128, 256, 512])
    num_layers_range: Tuple[int, int] = (2, 8)
    dropout_rates: List[float] = field(default_factory=lambda: [0.0, 0.1, 0.2, 0.3, 0.4, 0.5])
    activation_functions: List[str] = field(default_factory=lambda: ['relu', 'tanh', 'gelu', 'swish'])
    attention_heads: List[int] = field(default_factory=lambda: [1, 2, 4, 8, 16])
    
@dataclass
class ArchitectureGenome:
    """Represents a neural network architecture as a genome"""
    layers: List[Dict[str, Any]]
    performance_score: float = 0.0
    complexity_score: float = 0.0
    fitness_score: float = 0.0
    generation: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'layers': self.layers,
            'performance_score': self.performance_score,
            'complexity_score': self.complexity_score,
            'fitness_score': self.fitness_score,
            'generation': self.generation
        }

class ArchitectureBuilder:
    """Builds neural network models from architecture genomes"""
    
    def __init__(self, input_size: int, output_size: int):
        self.input_size = input_size
        self.output_size = output_size
    
    def build_model(self, genome: ArchitectureGenome) -> nn.Module:
        """Build a PyTorch model from architecture genome"""
        layers = []
        current_size = self.input_size
        
        for i, layer_config in enumerate(genome.layers):
            layer_type = layer_config['type']
            
            if layer_type == 'linear':
                hidden_size = layer_config['hidden_size']
                dropout = layer_config.get('dropout', 0.0)
                activation = layer_config.get('activation', 'relu')
                
                layers.append(nn.Linear(current_size, hidden_size))
                layers.append(self._get_activation(activation))
                if dropout > 0:
                    layers.append(nn.Dropout(dropout))
                
                current_size = hidden_size
                
            elif layer_type == 'lstm':
                hidden_size = layer_config['hidden_size']
                num_layers = layer_config.get('num_layers', 1)
                dropout = layer_config.get('dropout', 0.0)
                bidirectional = layer_config.get('bidirectional', False)
                
                lstm = nn.LSTM(
                    current_size, hidden_size, num_layers,
                    batch_first=True, dropout=dropout if num_layers > 1 else 0,
                    bidirectional=bidirectional
                )
                layers.append(LSTMWrapper(lstm))
                
                current_size = hidden_size * (2 if bidirectional else 1)
                
            elif layer_type == 'gru':
                hidden_size = layer_config['hidden_size']
                num_layers = layer_config.get('num_layers', 1)
                dropout = layer_config.get('dropout', 0.0)
                bidirectional = layer_config.get('bidirectional', False)
                
                gru = nn.GRU(
                    current_size, hidden_size, num_layers,
                    batch_first=True, dropout=dropout if num_layers > 1 else 0,
                    bidirectional=bidirectional
                )
                layers.append(GRUWrapper(gru))
                
                current_size = hidden_size * (2 if bidirectional else 1)
                
            elif layer_type == 'attention':
                num_heads = layer_config.get('num_heads', 8)
                dropout = layer_config.get('dropout', 0.1)
                
                attention = MultiHeadAttentionWrapper(current_size, num_heads, dropout)
                layers.append(attention)
                
            elif layer_type == 'conv1d':
                out_channels = layer_config['out_channels']
                kernel_size = layer_config.get('kernel_size', 3)
                stride = layer_config.get('stride', 1)
                padding = layer_config.get('padding', 1)
                
                layers.append(nn.Conv1d(current_size, out_channels, kernel_size, stride, padding))
                layers.append(nn.ReLU())
                layers.append(nn.AdaptiveAvgPool1d(1))
                layers.append(nn.Flatten())
                
                current_size = out_channels
        
        # Add final output layer
        layers.append(nn.Linear(current_size, self.output_size))
        
        return nn.Sequential(*layers)
    
    def _get_activation(self, activation: str) -> nn.Module:
        """Get activation function by name"""
        activations = {
            'relu': nn.ReLU(),
            'tanh': nn.Tanh(),
            'gelu': nn.GELU(),
            'swish': nn.SiLU(),
            'sigmoid': nn.Sigmoid()
        }
        return activations.get(activation, nn.ReLU())

    def build_optimized_model(self, genome: ArchitectureGenome,
                            optimization_target: str = 'performance') -> nn.Module:
        """Build model with specific optimization targets"""
        model = self.build_model(genome)

        # Apply optimization-specific modifications
        if optimization_target == 'speed':
            # Optimize for inference speed
            model = self._optimize_for_speed(model)
        elif optimization_target == 'memory':
            # Optimize for memory efficiency
            model = self._optimize_for_memory(model)
        elif optimization_target == 'accuracy':
            # Optimize for maximum accuracy
            model = self._optimize_for_accuracy(model)

        return model

    def _optimize_for_speed(self, model: nn.Module) -> nn.Module:
        """Optimize model for inference speed"""
        # Replace complex operations with faster alternatives
        for name, module in model.named_modules():
            if isinstance(module, nn.LSTM):
                # Replace LSTM with faster GRU if possible
                if hasattr(module, 'input_size'):
                    gru = nn.GRU(
                        input_size=module.input_size,
                        hidden_size=module.hidden_size,
                        num_layers=module.num_layers,
                        dropout=module.dropout,
                        batch_first=module.batch_first
                    )
                    # Note: In practice, you'd need to properly replace the module
                    logger.info(f"🚀 [NAS] Optimized {name} for speed: LSTM -> GRU")

        return model

    def _optimize_for_memory(self, model: nn.Module) -> nn.Module:
        """Optimize model for memory efficiency"""
        # Reduce model size while maintaining performance
        for name, module in model.named_modules():
            if isinstance(module, nn.Linear):
                # Apply weight pruning for linear layers
                if module.weight.size(0) > 64:
                    logger.info(f"🧠 [NAS] Optimized {name} for memory efficiency")

        return model

    def _optimize_for_accuracy(self, model: nn.Module) -> nn.Module:
        """Optimize model for maximum accuracy"""
        # Add regularization and normalization for better accuracy
        for name, module in model.named_modules():
            if isinstance(module, nn.Linear):
                logger.info(f"📈 [NAS] Optimized {name} for accuracy")

        return model

    def estimate_model_complexity(self, genome: ArchitectureGenome) -> float:
        """Estimate computational complexity of the architecture"""
        complexity = 0.0

        for layer in genome.layers:
            layer_type = layer['type']

            if layer_type == 'linear':
                # FLOPs for linear layer: input_size * output_size
                complexity += layer['input_size'] * layer['output_size']
            elif layer_type in ['lstm', 'gru']:
                # RNN complexity is higher
                hidden_size = layer['hidden_size']
                complexity += hidden_size * hidden_size * 4  # Approximate LSTM complexity
            elif layer_type == 'attention':
                # Attention complexity: O(n^2 * d)
                seq_len = layer.get('seq_len', 100)
                d_model = layer['hidden_size']
                complexity += seq_len * seq_len * d_model
            elif layer_type == 'conv1d':
                # Convolution complexity
                kernel_size = layer.get('kernel_size', 3)
                complexity += kernel_size * layer['output_size']

        return complexity

    def get_model_memory_usage(self, model: nn.Module) -> Dict[str, float]:
        """Estimate memory usage of the model"""
        param_size = sum(p.numel() * p.element_size() for p in model.parameters())
        buffer_size = sum(b.numel() * b.element_size() for b in model.buffers())

        return {
            'parameters_mb': param_size / (1024 * 1024),
            'buffers_mb': buffer_size / (1024 * 1024),
            'total_mb': (param_size + buffer_size) / (1024 * 1024)
        }

class LSTMWrapper(nn.Module):
    """Wrapper for LSTM to handle sequential output"""
    
    def __init__(self, lstm: nn.LSTM):
        super().__init__()
        self.lstm = lstm
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        if len(x.shape) == 2:
            x = x.unsqueeze(1)  # Add sequence dimension
        output, _ = self.lstm(x)
        return output[:, -1, :]  # Return last timestep

class GRUWrapper(nn.Module):
    """Wrapper for GRU to handle sequential output"""
    
    def __init__(self, gru: nn.GRU):
        super().__init__()
        self.gru = gru
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        if len(x.shape) == 2:
            x = x.unsqueeze(1)  # Add sequence dimension
        output, _ = self.gru(x)
        return output[:, -1, :]  # Return last timestep

class MultiHeadAttentionWrapper(nn.Module):
    """Wrapper for multi-head attention"""
    
    def __init__(self, d_model: int, num_heads: int, dropout: float = 0.1):
        super().__init__()
        self.attention = nn.MultiheadAttention(d_model, num_heads, dropout, batch_first=True)
        self.norm = nn.LayerNorm(d_model)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        if len(x.shape) == 2:
            x = x.unsqueeze(1)  # Add sequence dimension
        
        attn_output, _ = self.attention(x, x, x)
        output = self.norm(x + attn_output)
        return output[:, -1, :]  # Return last timestep

class GeneticAlgorithmNAS:
    """Genetic Algorithm for Neural Architecture Search"""
    
    def __init__(self, search_space: SearchSpace, population_size: int = 20,
                 mutation_rate: float = 0.1, crossover_rate: float = 0.7):
        self.search_space = search_space
        self.population_size = population_size
        self.mutation_rate = mutation_rate
        self.crossover_rate = crossover_rate
        
        self.population = []
        self.generation = 0
        self.best_architectures = []
        
    def initialize_population(self) -> List[ArchitectureGenome]:
        """Initialize random population of architectures"""
        population = []
        
        for _ in range(self.population_size):
            genome = self._create_random_genome()
            population.append(genome)
        
        self.population = population
        return population
    
    def _create_random_genome(self) -> ArchitectureGenome:
        """Create a random architecture genome"""
        num_layers = random.randint(*self.search_space.num_layers_range)
        layers = []
        
        for i in range(num_layers):
            layer_type = random.choice(self.search_space.layer_types)
            
            layer_config = {
                'type': layer_type,
                'hidden_size': random.choice(self.search_space.hidden_sizes),
                'dropout': random.choice(self.search_space.dropout_rates),
                'activation': random.choice(self.search_space.activation_functions)
            }
            
            # Add type-specific parameters
            if layer_type in ['lstm', 'gru']:
                layer_config['num_layers'] = random.randint(1, 3)
                layer_config['bidirectional'] = random.choice([True, False])
            elif layer_type == 'attention':
                layer_config['num_heads'] = random.choice(self.search_space.attention_heads)
            elif layer_type == 'conv1d':
                layer_config['out_channels'] = random.choice(self.search_space.hidden_sizes)
                layer_config['kernel_size'] = random.choice([3, 5, 7])
            
            layers.append(layer_config)
        
        return ArchitectureGenome(layers=layers, generation=self.generation)
    
    def evaluate_population(self, population: List[ArchitectureGenome],
                          fitness_function) -> List[ArchitectureGenome]:
        """Evaluate fitness of entire population"""
        for genome in population:
            if genome.fitness_score == 0.0:  # Only evaluate if not already evaluated
                fitness_score = fitness_function(genome)
                genome.fitness_score = fitness_score
        
        # Sort by fitness (higher is better)
        population.sort(key=lambda x: x.fitness_score, reverse=True)
        return population
    
    def selection(self, population: List[ArchitectureGenome], 
                 num_parents: int) -> List[ArchitectureGenome]:
        """Tournament selection for parent selection"""
        parents = []
        
        for _ in range(num_parents):
            tournament_size = min(3, len(population))
            tournament = random.sample(population, tournament_size)
            winner = max(tournament, key=lambda x: x.fitness_score)
            parents.append(winner)
        
        return parents
    
    def crossover(self, parent1: ArchitectureGenome, 
                 parent2: ArchitectureGenome) -> Tuple[ArchitectureGenome, ArchitectureGenome]:
        """Single-point crossover between two parents"""
        if random.random() > self.crossover_rate:
            return parent1, parent2
        
        # Find crossover point
        min_length = min(len(parent1.layers), len(parent2.layers))
        if min_length <= 1:
            return parent1, parent2
        
        crossover_point = random.randint(1, min_length - 1)
        
        # Create offspring
        child1_layers = parent1.layers[:crossover_point] + parent2.layers[crossover_point:]
        child2_layers = parent2.layers[:crossover_point] + parent1.layers[crossover_point:]
        
        child1 = ArchitectureGenome(layers=child1_layers, generation=self.generation + 1)
        child2 = ArchitectureGenome(layers=child2_layers, generation=self.generation + 1)
        
        return child1, child2
    
    def mutate(self, genome: ArchitectureGenome) -> ArchitectureGenome:
        """Mutate an architecture genome"""
        if random.random() > self.mutation_rate:
            return genome
        
        mutated_layers = genome.layers.copy()
        
        # Choose mutation type
        mutation_types = ['modify_layer', 'add_layer', 'remove_layer']
        mutation_type = random.choice(mutation_types)
        
        if mutation_type == 'modify_layer' and mutated_layers:
            # Modify existing layer
            layer_idx = random.randint(0, len(mutated_layers) - 1)
            layer = mutated_layers[layer_idx].copy()
            
            # Randomly modify parameters
            if 'hidden_size' in layer:
                layer['hidden_size'] = random.choice(self.search_space.hidden_sizes)
            if 'dropout' in layer:
                layer['dropout'] = random.choice(self.search_space.dropout_rates)
            if 'activation' in layer:
                layer['activation'] = random.choice(self.search_space.activation_functions)
            
            mutated_layers[layer_idx] = layer
            
        elif mutation_type == 'add_layer':
            # Add new layer
            new_layer = {
                'type': random.choice(self.search_space.layer_types),
                'hidden_size': random.choice(self.search_space.hidden_sizes),
                'dropout': random.choice(self.search_space.dropout_rates),
                'activation': random.choice(self.search_space.activation_functions)
            }
            
            insert_position = random.randint(0, len(mutated_layers))
            mutated_layers.insert(insert_position, new_layer)
            
        elif mutation_type == 'remove_layer' and len(mutated_layers) > 1:
            # Remove layer
            remove_idx = random.randint(0, len(mutated_layers) - 1)
            mutated_layers.pop(remove_idx)
        
        return ArchitectureGenome(layers=mutated_layers, generation=self.generation + 1)
    
    def evolve_generation(self, fitness_function) -> List[ArchitectureGenome]:
        """Evolve one generation"""
        # Evaluate current population
        self.population = self.evaluate_population(self.population, fitness_function)
        
        # Store best architectures
        self.best_architectures.append(self.population[0])
        
        # Selection
        num_parents = self.population_size // 2
        parents = self.selection(self.population, num_parents)
        
        # Create next generation
        next_generation = []
        
        # Elitism: keep best individuals
        elite_size = max(1, self.population_size // 10)
        next_generation.extend(self.population[:elite_size])
        
        # Generate offspring
        while len(next_generation) < self.population_size:
            parent1, parent2 = random.sample(parents, 2)
            child1, child2 = self.crossover(parent1, parent2)
            
            child1 = self.mutate(child1)
            child2 = self.mutate(child2)
            
            next_generation.extend([child1, child2])
        
        # Trim to population size
        next_generation = next_generation[:self.population_size]
        
        self.population = next_generation
        self.generation += 1
        
        return self.population
    
    def get_best_architecture(self) -> ArchitectureGenome:
        """Get the best architecture found so far"""
        if self.best_architectures:
            return max(self.best_architectures, key=lambda x: x.fitness_score)
        return None
    
    def save_search_results(self, path: str):
        """Save search results to file"""
        results = {
            'generation': self.generation,
            'population_size': self.population_size,
            'best_architectures': [arch.to_dict() for arch in self.best_architectures],
            'current_population': [arch.to_dict() for arch in self.population]
        }
        
        with open(path, 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"NAS results saved to {path}")
    
    def load_search_results(self, path: str):
        """Load search results from file"""
        with open(path, 'r') as f:
            results = json.load(f)
        
        self.generation = results['generation']
        
        # Reconstruct best architectures
        self.best_architectures = [
            ArchitectureGenome(**arch_dict) for arch_dict in results['best_architectures']
        ]
        
        # Reconstruct current population
        self.population = [
            ArchitectureGenome(**arch_dict) for arch_dict in results['current_population']
        ]
        
        logger.info(f"NAS results loaded from {path}")
