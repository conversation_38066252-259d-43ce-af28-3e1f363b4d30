#!/usr/bin/env python3
"""
Final comprehensive script to fix ALL indentation issues
"""

import re

def fix_all_indentation_final():
    """Fix all indentation issues comprehensively"""
    
    with open('main_cleaned.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    fixed_lines = []
    in_class = False
    class_indent = 0
    
    for i, line in enumerate(lines):
        line_num = i + 1
        stripped = line.strip()
        leading_spaces = len(line) - len(line.lstrip())
        
        # Skip empty lines
        if not stripped:
            fixed_lines.append(line)
            continue
        
        # Track class definitions
        if stripped.startswith('class '):
            in_class = True
            class_indent = leading_spaces
            fixed_lines.append(line)
            continue
        
        # Fix method definitions - should be at class level (4 spaces)
        if re.match(r'\s*(async\s+)?def\s+', line):
            method_match = re.match(r'\s*(async\s+def\s+.*|def\s+.*)', line)
            if method_match:
                method_def = method_match.group(1)
                if in_class:
                    # Class method - 4 spaces
                    fixed_lines.append('    ' + method_def + '\n')
                else:
                    # Module function - 0 spaces
                    fixed_lines.append(method_def + '\n')
                continue
        
        # Handle module-level code
        if leading_spaces == 0 and stripped and not stripped.startswith('#'):
            if any(stripped.startswith(x) for x in ['import ', 'from ', 'if __name__', 'try:', 'except', 'finally:']):
                in_class = False
                fixed_lines.append(line)
                continue
        
        # Normal line - keep as is
        fixed_lines.append(line)
    
    # Write the fixed file
    with open('main_final_fixed.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print(f"Final comprehensive fix completed")
    print(f"Output saved to main_final_fixed.py")
    print(f"Processed {len(lines)} lines")

if __name__ == "__main__":
    fix_all_indentation_final()
