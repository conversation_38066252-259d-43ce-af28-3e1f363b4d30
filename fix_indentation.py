#!/usr/bin/env python3
"""
Quick script to fix the indentation issues in main.py
"""

import re

def fix_indentation():
    """Fix the indentation issues in main.py"""
    
    # Read the file
    with open('main.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    fixed_lines = []
    in_method = False
    method_indent = 0
    
    for i, line in enumerate(lines):
        line_num = i + 1
        
        # Check if this is a method definition
        if re.match(r'\s*async def ', line) or re.match(r'\s*def ', line):
            # This should be at class level (4 spaces) or module level (0 spaces)
            if line.strip().startswith('async def _') or line.strip().startswith('def _'):
                # Private method - should be at class level
                fixed_line = '    ' + line.strip() + '\n'
            else:
                # Public method or function
                fixed_line = line.strip() + '\n'
            
            fixed_lines.append(fixed_line)
            in_method = True
            method_indent = 4 if line.strip().startswith('async def _') or line.strip().startswith('def _') else 0
            continue
        
        # If we're in a method, fix the indentation
        if in_method:
            stripped = line.strip()
            
            # Empty lines
            if not stripped:
                fixed_lines.append('\n')
                continue
            
            # Comments and docstrings
            if stripped.startswith('"""') or stripped.startswith("'''"):
                fixed_lines.append(' ' * (method_indent + 4) + stripped + '\n')
                continue
            
            # Method body should be indented relative to method definition
            if stripped:
                # Calculate appropriate indentation based on content
                base_indent = method_indent + 4
                
                # Handle special cases
                if stripped.startswith('except') or stripped.startswith('finally'):
                    base_indent = method_indent + 4
                elif stripped.startswith('elif') or stripped.startswith('else'):
                    base_indent = method_indent + 4
                elif stripped.startswith('try:'):
                    base_indent = method_indent + 4
                
                # Count additional indentation for nested blocks
                original_leading_spaces = len(line) - len(line.lstrip())
                if original_leading_spaces > 20:  # Likely over-indented
                    # Reduce excessive indentation
                    extra_indent = min(original_leading_spaces - 20, 8)
                else:
                    extra_indent = 0
                
                final_indent = base_indent + extra_indent
                fixed_lines.append(' ' * final_indent + stripped + '\n')
                continue
        
        # If we're not in a method, keep the line as is (but check for orphaned code)
        if line.strip() and not line.startswith(' ' * 20):  # Not heavily indented orphaned code
            fixed_lines.append(line)
            in_method = False
        elif line.strip():
            # This might be orphaned code - try to fix it
            stripped = line.strip()
            if stripped.startswith('#'):
                # Comment - keep minimal indentation
                fixed_lines.append('    ' + stripped + '\n')
            else:
                # Code - this might be orphaned, skip it or fix it
                print(f"Warning: Possible orphaned code at line {line_num}: {stripped[:50]}...")
                # Skip heavily indented orphaned code
                continue
        else:
            # Empty line
            fixed_lines.append(line)
    
    # Write the fixed file
    with open('main_fixed.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print("Fixed indentation issues. Output saved to main_fixed.py")
    print("Please review the changes before replacing main.py")

if __name__ == "__main__":
    fix_indentation()
