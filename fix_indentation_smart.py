#!/usr/bin/env python3
"""
Smart script to fix indentation issues without breaking syntax
"""

import re

def fix_indentation_smart():
    """Fix indentation issues by commenting out entire orphaned blocks"""
    
    with open('main.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    fixed_lines = []
    in_method = False
    method_indent = 0
    orphaned_block = False
    
    for i, line in enumerate(lines):
        line_num = i + 1
        stripped = line.strip()
        leading_spaces = len(line) - len(line.lstrip())
        
        # Skip empty lines
        if not stripped:
            fixed_lines.append(line)
            continue
        
        # Track method definitions
        if re.match(r'\s*(async\s+)?def\s+', line):
            in_method = True
            method_indent = leading_spaces
            orphaned_block = False
            fixed_lines.append(line)
            continue
        
        # Track class definitions
        if stripped.startswith('class '):
            in_method = False
            orphaned_block = False
            fixed_lines.append(line)
            continue
        
        # Track module-level code
        if leading_spaces == 0 and stripped and not stripped.startswith('#'):
            in_method = False
            orphaned_block = False
            fixed_lines.append(line)
            continue
        
        # If we're in a method, check for orphaned code
        if in_method:
            expected_min_indent = method_indent + 4
            
            # If the line is way over-indented, start orphaned block
            if leading_spaces > expected_min_indent + 12:
                if not orphaned_block:
                    fixed_lines.append(f'            # ORPHANED CODE BLOCK COMMENTED OUT (line {line_num}):\n')
                    orphaned_block = True
                fixed_lines.append('            # ' + line.lstrip())
                continue
            else:
                orphaned_block = False
        
        # If we're not in a method and heavily indented, it's orphaned
        elif not in_method and leading_spaces > 8:
            if not orphaned_block:
                fixed_lines.append(f'# ORPHANED CODE BLOCK COMMENTED OUT (line {line_num}):\n')
                orphaned_block = True
            fixed_lines.append('# ' + line.lstrip())
            continue
        else:
            orphaned_block = False
        
        # Normal line
        fixed_lines.append(line)
    
    # Write the fixed file
    with open('main_smart_fixed.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print(f"Smart fix completed. Output saved to main_smart_fixed.py")
    print(f"Processed {len(lines)} lines")

if __name__ == "__main__":
    fix_indentation_smart()
