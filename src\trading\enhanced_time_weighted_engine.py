"""
Enhanced Time-Weighted Decision Engine
Advanced temporal analysis and time-optimized trading strategies
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timezone, timedelta
import numpy as np
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class TimeOfDayStrategy(Enum):
    """Time-of-day specific trading strategies"""
    ASIAN_SESSION = "asian_session"      # 00:00-08:00 UTC
    LONDON_OPEN = "london_open"          # 08:00-12:00 UTC
    NY_LONDON_OVERLAP = "ny_london"      # 12:00-17:00 UTC
    NY_SESSION = "ny_session"            # 17:00-22:00 UTC
    AFTER_HOURS = "after_hours"          # 22:00-00:00 UTC
    WEEKEND = "weekend"                  # Saturday-Sunday

class MarketRegime(Enum):
    """Market regime classifications"""
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    TRENDING = "trending"
    RANGING = "ranging"
    NEWS_DRIVEN = "news_driven"
    ALGORITHMIC = "algorithmic"

@dataclass
class TimeContext:
    """Complete time context for trading decisions"""
    timestamp: datetime
    session: TimeOfDayStrategy
    session_progress: float  # 0.0 to 1.0
    market_regime: MarketRegime
    volatility_factor: float
    liquidity_factor: float
    volume_factor: float
    optimal_execution_window: bool
    time_decay_factor: float
    urgency_score: float

@dataclass
class TimeOptimizedOrder:
    """Time-optimized order with execution timing"""
    symbol: str
    side: str
    amount: Decimal
    order_type: str
    execution_strategy: str
    optimal_start_time: datetime
    optimal_end_time: datetime
    time_slices: List[Dict[str, Any]]
    expected_slippage: float
    time_risk_score: float

class EnhancedTimeWeightedEngine:
    """Enhanced time-weighted decision engine with advanced temporal analysis"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        
        # Time analysis parameters
        self.session_boundaries = {
            TimeOfDayStrategy.ASIAN_SESSION: (0, 8),
            TimeOfDayStrategy.LONDON_OPEN: (8, 12),
            TimeOfDayStrategy.NY_LONDON_OVERLAP: (12, 17),
            TimeOfDayStrategy.NY_SESSION: (17, 22),
            TimeOfDayStrategy.AFTER_HOURS: (22, 24)
        }
        
        # Historical performance tracking
        self.session_performance = defaultdict(lambda: defaultdict(list))
        self.time_patterns = defaultdict(list)
        self.execution_history = deque(maxlen=10000)
        
        # Market regime detection
        self.regime_indicators = {}
        self.regime_history = deque(maxlen=1000)
        
        # Time-based risk management
        self.time_risk_limits = {
            'max_position_during_news': 0.5,
            'max_position_after_hours': 0.3,
            'max_position_weekend': 0.1
        }
        
        logger.info("⏰ [TIME-ENGINE] Enhanced time-weighted decision engine initialized")

    async def analyze_time_context(self, market_data: Dict[str, Any]) -> TimeContext:
        """Analyze current time context for trading decisions"""
        try:
            current_time = datetime.now(timezone.utc)
            
            # Determine current session
            session = self._get_current_session(current_time)
            session_progress = self._calculate_session_progress(current_time, session)
            
            # Detect market regime
            market_regime = await self._detect_market_regime(market_data)
            
            # Calculate time-based factors
            volatility_factor = await self._calculate_volatility_factor(current_time, market_data)
            liquidity_factor = await self._calculate_liquidity_factor(current_time, session)
            volume_factor = await self._calculate_volume_factor(current_time, market_data)
            
            # Determine optimal execution window
            optimal_window = await self._is_optimal_execution_window(current_time, session, market_regime)
            
            # Calculate time decay and urgency
            time_decay = self._calculate_time_decay_factor(current_time)
            urgency = await self._calculate_urgency_score(current_time, market_data)
            
            return TimeContext(
                timestamp=current_time,
                session=session,
                session_progress=session_progress,
                market_regime=market_regime,
                volatility_factor=volatility_factor,
                liquidity_factor=liquidity_factor,
                volume_factor=volume_factor,
                optimal_execution_window=optimal_window,
                time_decay_factor=time_decay,
                urgency_score=urgency
            )
            
        except Exception as e:
            logger.error(f"❌ [TIME-ENGINE] Error analyzing time context: {e}")
            return self._get_default_time_context()

    def _get_current_session(self, current_time: datetime) -> TimeOfDayStrategy:
        """Determine current trading session"""
        try:
            hour = current_time.hour
            weekday = current_time.weekday()
            
            # Weekend handling
            if weekday >= 5:  # Saturday = 5, Sunday = 6
                return TimeOfDayStrategy.WEEKEND
            
            # Determine session based on hour
            for session, (start_hour, end_hour) in self.session_boundaries.items():
                if start_hour <= hour < end_hour:
                    return session
            
            return TimeOfDayStrategy.AFTER_HOURS
            
        except Exception as e:
            logger.error(f"❌ [TIME-ENGINE] Error determining session: {e}")
            return TimeOfDayStrategy.NY_SESSION

    def _calculate_session_progress(self, current_time: datetime, session: TimeOfDayStrategy) -> float:
        """Calculate progress through current session (0.0 to 1.0)"""
        try:
            if session == TimeOfDayStrategy.WEEKEND:
                return 0.5
            
            hour = current_time.hour
            minute = current_time.minute
            current_decimal_hour = hour + minute / 60.0
            
            if session in self.session_boundaries:
                start_hour, end_hour = self.session_boundaries[session]
                session_duration = end_hour - start_hour
                progress = (current_decimal_hour - start_hour) / session_duration
                return max(0.0, min(1.0, progress))
            
            return 0.5
            
        except Exception as e:
            logger.error(f"❌ [TIME-ENGINE] Error calculating session progress: {e}")
            return 0.5

    async def _detect_market_regime(self, market_data: Dict[str, Any]) -> MarketRegime:
        """Detect current market regime"""
        try:
            # Extract volatility indicators
            volatility = market_data.get('volatility', 0.02)
            volume = market_data.get('volume', 0)
            price_change = market_data.get('price_change_24h', 0)
            
            # High volatility regime
            if volatility > 0.05:
                return MarketRegime.HIGH_VOLATILITY
            
            # Low volatility regime
            elif volatility < 0.01:
                return MarketRegime.LOW_VOLATILITY
            
            # Trending regime
            elif abs(price_change) > 0.03:
                return MarketRegime.TRENDING
            
            # News-driven regime (high volume with high volatility)
            elif volume > 1000000 and volatility > 0.03:
                return MarketRegime.NEWS_DRIVEN
            
            # Algorithmic regime (low volume, low volatility)
            elif volume < 100000 and volatility < 0.015:
                return MarketRegime.ALGORITHMIC
            
            # Default to ranging
            else:
                return MarketRegime.RANGING
                
        except Exception as e:
            logger.error(f"❌ [TIME-ENGINE] Error detecting market regime: {e}")
            return MarketRegime.RANGING

    async def _calculate_volatility_factor(self, current_time: datetime, 
                                         market_data: Dict[str, Any]) -> float:
        """Calculate time-adjusted volatility factor"""
        try:
            base_volatility = market_data.get('volatility', 0.02)
            hour = current_time.hour
            
            # Time-of-day volatility adjustments
            if 12 <= hour <= 17:  # NY-London overlap - highest volatility
                time_multiplier = 1.3
            elif 8 <= hour <= 12:  # London open - high volatility
                time_multiplier = 1.2
            elif 17 <= hour <= 22:  # NY session - moderate volatility
                time_multiplier = 1.1
            elif 0 <= hour <= 8:   # Asian session - lower volatility
                time_multiplier = 0.8
            else:  # After hours - lowest volatility
                time_multiplier = 0.6
            
            return base_volatility * time_multiplier
            
        except Exception as e:
            logger.error(f"❌ [TIME-ENGINE] Error calculating volatility factor: {e}")
            return 0.02

    async def _calculate_liquidity_factor(self, current_time: datetime, 
                                        session: TimeOfDayStrategy) -> float:
        """Calculate time-adjusted liquidity factor"""
        try:
            # Base liquidity scores by session
            session_liquidity = {
                TimeOfDayStrategy.NY_LONDON_OVERLAP: 1.0,  # Highest liquidity
                TimeOfDayStrategy.LONDON_OPEN: 0.9,
                TimeOfDayStrategy.NY_SESSION: 0.8,
                TimeOfDayStrategy.ASIAN_SESSION: 0.6,
                TimeOfDayStrategy.AFTER_HOURS: 0.4,
                TimeOfDayStrategy.WEEKEND: 0.2  # Lowest liquidity
            }
            
            return session_liquidity.get(session, 0.5)
            
        except Exception as e:
            logger.error(f"❌ [TIME-ENGINE] Error calculating liquidity factor: {e}")
            return 0.5

    async def _calculate_volume_factor(self, current_time: datetime, 
                                     market_data: Dict[str, Any]) -> float:
        """Calculate time-adjusted volume factor"""
        try:
            current_volume = market_data.get('volume', 0)
            if current_volume <= 0:
                return 0.5
            
            # Get historical average volume for this time
            hour = current_time.hour
            historical_avg = await self._get_historical_volume_average(hour)
            
            if historical_avg > 0:
                volume_ratio = current_volume / historical_avg
                # Normalize to 0-1 scale
                return min(1.0, max(0.1, volume_ratio / 2.0))
            
            return 0.5
            
        except Exception as e:
            logger.error(f"❌ [TIME-ENGINE] Error calculating volume factor: {e}")
            return 0.5

    async def _is_optimal_execution_window(self, current_time: datetime, 
                                         session: TimeOfDayStrategy,
                                         regime: MarketRegime) -> bool:
        """Determine if current time is optimal for execution"""
        try:
            # Optimal windows by session
            if session == TimeOfDayStrategy.NY_LONDON_OVERLAP:
                return True  # Always optimal during overlap
            elif session == TimeOfDayStrategy.LONDON_OPEN:
                return regime != MarketRegime.HIGH_VOLATILITY
            elif session == TimeOfDayStrategy.NY_SESSION:
                return regime in [MarketRegime.TRENDING, MarketRegime.RANGING]
            elif session == TimeOfDayStrategy.WEEKEND:
                return False  # Never optimal on weekends
            else:
                return regime == MarketRegime.LOW_VOLATILITY
                
        except Exception as e:
            logger.error(f"❌ [TIME-ENGINE] Error determining optimal window: {e}")
            return False

    def _calculate_time_decay_factor(self, current_time: datetime) -> float:
        """Calculate time decay factor for decision urgency"""
        try:
            # Time decay based on distance from optimal trading hours
            hour = current_time.hour
            
            # Peak hours (12-17 UTC) have lowest decay
            if 12 <= hour <= 17:
                return 1.0
            # Good hours have moderate decay
            elif 8 <= hour <= 22:
                distance_from_peak = min(abs(hour - 12), abs(hour - 17))
                return 1.0 - (distance_from_peak / 10.0)
            # Off hours have high decay
            else:
                return 0.3
                
        except Exception as e:
            logger.error(f"❌ [TIME-ENGINE] Error calculating time decay: {e}")
            return 0.5

    async def _calculate_urgency_score(self, current_time: datetime, 
                                     market_data: Dict[str, Any]) -> float:
        """Calculate execution urgency score"""
        try:
            urgency = 0.5  # Base urgency
            
            # Increase urgency based on volatility
            volatility = market_data.get('volatility', 0.02)
            if volatility > 0.05:
                urgency += 0.3
            elif volatility > 0.03:
                urgency += 0.2
            
            # Increase urgency near session boundaries
            session = self._get_current_session(current_time)
            session_progress = self._calculate_session_progress(current_time, session)
            
            if session_progress > 0.8:  # Near session end
                urgency += 0.2
            elif session_progress < 0.2:  # Near session start
                urgency += 0.1
            
            # Increase urgency for large price movements
            price_change = abs(market_data.get('price_change_1h', 0))
            if price_change > 0.02:
                urgency += 0.2
            
            return min(1.0, urgency)
            
        except Exception as e:
            logger.error(f"❌ [TIME-ENGINE] Error calculating urgency: {e}")
            return 0.5

    async def optimize_execution_timing(self, symbol: str, side: str, amount: Decimal,
                                      time_context: TimeContext) -> TimeOptimizedOrder:
        """Optimize execution timing for an order"""
        try:
            current_time = time_context.timestamp
            
            # Determine optimal execution strategy
            execution_strategy = await self._select_execution_strategy(time_context)
            
            # Calculate optimal time window
            optimal_start, optimal_end = await self._calculate_optimal_window(
                current_time, time_context, execution_strategy
            )
            
            # Create time slices for execution
            time_slices = await self._create_time_slices(
                amount, optimal_start, optimal_end, execution_strategy, time_context
            )
            
            # Estimate execution costs
            expected_slippage = await self._estimate_time_based_slippage(
                symbol, time_context, execution_strategy
            )
            
            # Calculate time risk score
            time_risk = await self._calculate_time_risk_score(time_context, execution_strategy)
            
            return TimeOptimizedOrder(
                symbol=symbol,
                side=side,
                amount=amount,
                order_type=execution_strategy,
                execution_strategy=execution_strategy,
                optimal_start_time=optimal_start,
                optimal_end_time=optimal_end,
                time_slices=time_slices,
                expected_slippage=expected_slippage,
                time_risk_score=time_risk
            )
            
        except Exception as e:
            logger.error(f"❌ [TIME-ENGINE] Error optimizing execution timing: {e}")
            return self._get_default_optimized_order(symbol, side, amount)

    async def _select_execution_strategy(self, time_context: TimeContext) -> str:
        """Select optimal execution strategy based on time context"""
        try:
            # High liquidity and optimal window - use aggressive execution
            if time_context.liquidity_factor > 0.8 and time_context.optimal_execution_window:
                return "AGGRESSIVE"
            
            # High urgency - use TWAP with short duration
            elif time_context.urgency_score > 0.7:
                return "TWAP_FAST"
            
            # High volatility - use VWAP to minimize impact
            elif time_context.volatility_factor > 0.04:
                return "VWAP"
            
            # Low liquidity - use patient execution
            elif time_context.liquidity_factor < 0.4:
                return "TWAP_SLOW"
            
            # Default to balanced TWAP
            else:
                return "TWAP_BALANCED"
                
        except Exception as e:
            logger.error(f"❌ [TIME-ENGINE] Error selecting execution strategy: {e}")
            return "TWAP_BALANCED"

    async def _get_historical_volume_average(self, hour: int) -> float:
        """Get historical average volume for specific hour"""
        try:
            # Simplified historical volume patterns
            # In production, this would query actual historical data
            volume_patterns = {
                0: 0.3, 1: 0.2, 2: 0.2, 3: 0.2, 4: 0.3, 5: 0.4,
                6: 0.5, 7: 0.6, 8: 0.8, 9: 0.9, 10: 0.9, 11: 0.9,
                12: 1.0, 13: 1.0, 14: 1.0, 15: 1.0, 16: 0.9, 17: 0.8,
                18: 0.7, 19: 0.6, 20: 0.5, 21: 0.4, 22: 0.3, 23: 0.3
            }
            
            return volume_patterns.get(hour, 0.5) * 1000000  # Base volume
            
        except Exception as e:
            logger.error(f"❌ [TIME-ENGINE] Error getting historical volume: {e}")
            return 500000

    def _get_default_time_context(self) -> TimeContext:
        """Get default time context when analysis fails"""
        return TimeContext(
            timestamp=datetime.now(timezone.utc),
            session=TimeOfDayStrategy.NY_SESSION,
            session_progress=0.5,
            market_regime=MarketRegime.RANGING,
            volatility_factor=0.02,
            liquidity_factor=0.5,
            volume_factor=0.5,
            optimal_execution_window=False,
            time_decay_factor=0.5,
            urgency_score=0.5
        )

    def _get_default_optimized_order(self, symbol: str, side: str, amount: Decimal) -> TimeOptimizedOrder:
        """Get default optimized order when optimization fails"""
        current_time = datetime.now(timezone.utc)
        return TimeOptimizedOrder(
            symbol=symbol,
            side=side,
            amount=amount,
            order_type="MARKET",
            execution_strategy="IMMEDIATE",
            optimal_start_time=current_time,
            optimal_end_time=current_time + timedelta(minutes=1),
            time_slices=[{
                'amount': amount,
                'execution_time': current_time,
                'strategy': 'MARKET'
            }],
            expected_slippage=0.001,
            time_risk_score=0.5
        )

# Export the main class
__all__ = ['EnhancedTimeWeightedEngine', 'TimeContext', 'TimeOptimizedOrder', 'TimeOfDayStrategy', 'MarketRegime']
