"""
High-Frequency Trading Strategy Manager
Implements ultra-fast trading strategies with microsecond precision
"""

import asyncio
import logging
import time
import json
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum
import numpy as np
from collections import deque, defaultdict

logger = logging.getLogger(__name__)

class HFTStrategyType(Enum):
    """High-frequency trading strategy types"""
    MARKET_MAKING = "market_making"
    MOMENTUM_SCALPING = "momentum_scalping"
    ARBITRAGE_SCALPING = "arbitrage_scalping"
    MEAN_REVERSION = "mean_reversion"
    LATENCY_ARBITRAGE = "latency_arbitrage"
    ORDER_FLOW_IMBALANCE = "order_flow_imbalance"

@dataclass
class HFTSignal:
    """High-frequency trading signal"""
    strategy_type: HFTStrategyType
    symbol: str
    side: str  # BUY/SELL
    confidence: float
    urgency: float  # 0-1, how quickly to execute
    expected_profit_bps: float  # Expected profit in basis points
    max_position_size: Decimal
    time_horizon_ms: int  # Expected holding time in milliseconds
    risk_score: float
    timestamp: float

class HFTStrategyManager:
    """Manages high-frequency trading strategies with microsecond precision"""
    
    def __init__(self, exchange_manager, config: Dict = None):
        self.exchange_manager = exchange_manager
        self.config = config or {}
        
        # Load HFT configuration
        self.hft_config = self._load_hft_config()
        
        # Strategy configuration
        self.enabled_strategies = self.config.get('enabled_strategies', [
            HFTStrategyType.MARKET_MAKING,
            HFTStrategyType.MOMENTUM_SCALPING,
            HFTStrategyType.ARBITRAGE_SCALPING
        ])
        
        # Performance targets
        self.target_latency_ms = self.hft_config.get('target_latency_ms', 50)
        self.max_latency_ms = self.hft_config.get('max_latency_ms', 200)
        self.min_profit_bps = self.hft_config.get('min_profit_bps', 5)  # 0.05%
        
        # Risk management
        self.max_position_size = Decimal(str(self.hft_config.get('max_position_size', 1000)))
        self.max_daily_loss = Decimal(str(self.hft_config.get('max_daily_loss', 100)))
        self.position_limits = self.hft_config.get('position_limits', {})
        
        # Market data
        self.price_feeds = {}
        self.order_books = {}
        self.trade_streams = {}
        self.last_update_times = {}
        
        # Strategy state
        self.active_positions = {}
        self.pending_orders = {}
        self.strategy_performance = defaultdict(lambda: {
            'trades': 0,
            'profit': Decimal('0'),
            'win_rate': 0.0,
            'avg_latency': 0.0
        })
        
        # Signal generation
        self.signal_queue = asyncio.Queue(maxsize=1000)
        self.execution_queue = asyncio.Queue(maxsize=500)
        
        # Performance monitoring
        self.latency_history = deque(maxlen=1000)
        self.execution_times = deque(maxlen=1000)
        
        logger.info("⚡ [HFT] High-frequency trading strategy manager initialized")

    def _load_hft_config(self) -> Dict:
        """Load HFT configuration from file"""
        try:
            with open('config/hft_config.json', 'r') as f:
                config = json.load(f)
            return config
        except Exception as e:
            logger.warning(f"⚠️ [HFT] Could not load HFT config: {e}")
            return {
                'target_latency_ms': 50,
                'max_latency_ms': 200,
                'min_profit_bps': 5,
                'max_position_size': 1000,
                'max_daily_loss': 100
            }

    async def start_hft_engine(self):
        """Start the high-frequency trading engine"""
        try:
            logger.info("🚀 [HFT] Starting high-frequency trading engine...")
            
            # Start market data feeds
            await self._start_market_data_feeds()
            
            # Start strategy engines
            tasks = [
                asyncio.create_task(self._market_making_engine()),
                asyncio.create_task(self._momentum_scalping_engine()),
                asyncio.create_task(self._arbitrage_scalping_engine()),
                asyncio.create_task(self._signal_processor()),
                asyncio.create_task(self._execution_engine()),
                asyncio.create_task(self._performance_monitor())
            ]
            
            logger.info("✅ [HFT] High-frequency trading engine started")
            
            # Run all engines concurrently
            await asyncio.gather(*tasks, return_exceptions=True)
            
        except Exception as e:
            logger.error(f"❌ [HFT] Error starting HFT engine: {e}")

    async def _start_market_data_feeds(self):
        """Start real-time market data feeds"""
        try:
            # Get active exchanges
            active_exchanges = [name for name, status in self.exchange_manager.exchange_status.items() 
                              if status.active]
            
            # Start price feeds for major symbols
            symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'SOLUSDT', 'DOTUSDT']
            
            for exchange_name in active_exchanges:
                if exchange_name in self.exchange_manager.exchanges:
                    exchange = self.exchange_manager.exchanges[exchange_name]
                    
                    for symbol in symbols:
                        # Initialize price feed
                        feed_key = f"{exchange_name}_{symbol}"
                        self.price_feeds[feed_key] = deque(maxlen=1000)
                        self.last_update_times[feed_key] = 0
                        
                        # Start order book feed
                        asyncio.create_task(self._update_order_book(exchange, exchange_name, symbol))
            
            logger.info("✅ [HFT] Market data feeds started")
            
        except Exception as e:
            logger.error(f"❌ [HFT] Error starting market data feeds: {e}")

    async def _update_order_book(self, exchange, exchange_name: str, symbol: str):
        """Continuously update order book for a symbol"""
        try:
            while True:
                start_time = time.time()
                
                # Fetch order book
                order_book = await exchange.get_order_book(symbol, limit=20)
                
                if order_book and order_book.get('bids') and order_book.get('asks'):
                    book_key = f"{exchange_name}_{symbol}"
                    self.order_books[book_key] = order_book
                    self.last_update_times[book_key] = time.time()
                    
                    # Calculate latency
                    latency = (time.time() - start_time) * 1000
                    self.latency_history.append(latency)
                    
                    # Update price feed
                    if book_key in self.price_feeds:
                        mid_price = (order_book['bids'][0][0] + order_book['asks'][0][0]) / 2
                        self.price_feeds[book_key].append({
                            'price': mid_price,
                            'timestamp': time.time(),
                            'bid': order_book['bids'][0][0],
                            'ask': order_book['asks'][0][0],
                            'spread': order_book['asks'][0][0] - order_book['bids'][0][0]
                        })
                
                # High-frequency update interval
                await asyncio.sleep(0.1)  # 100ms updates
                
        except Exception as e:
            logger.debug(f"🔍 [HFT] Order book update error for {symbol}: {e}")

    async def _market_making_engine(self):
        """Market making strategy engine"""
        try:
            while True:
                if HFTStrategyType.MARKET_MAKING not in self.enabled_strategies:
                    await asyncio.sleep(1)
                    continue
                
                # Analyze all symbols for market making opportunities
                for book_key, order_book in self.order_books.items():
                    if not order_book or not order_book.get('bids') or not order_book.get('asks'):
                        continue
                    
                    exchange_name, symbol = book_key.split('_', 1)
                    
                    # Calculate spread and market making opportunity
                    bid_price = order_book['bids'][0][0]
                    ask_price = order_book['asks'][0][0]
                    spread = ask_price - bid_price
                    spread_bps = (spread / bid_price) * 10000
                    
                    # Check if spread is wide enough for market making
                    if spread_bps >= self.min_profit_bps * 2:  # Need 2x min profit for both sides
                        # Generate market making signals
                        buy_signal = HFTSignal(
                            strategy_type=HFTStrategyType.MARKET_MAKING,
                            symbol=symbol,
                            side='BUY',
                            confidence=0.8,
                            urgency=0.3,  # Low urgency for market making
                            expected_profit_bps=spread_bps / 2,
                            max_position_size=self.max_position_size * Decimal('0.1'),
                            time_horizon_ms=30000,  # 30 seconds
                            risk_score=0.2,
                            timestamp=time.time()
                        )
                        
                        sell_signal = HFTSignal(
                            strategy_type=HFTStrategyType.MARKET_MAKING,
                            symbol=symbol,
                            side='SELL',
                            confidence=0.8,
                            urgency=0.3,
                            expected_profit_bps=spread_bps / 2,
                            max_position_size=self.max_position_size * Decimal('0.1'),
                            time_horizon_ms=30000,
                            risk_score=0.2,
                            timestamp=time.time()
                        )
                        
                        # Queue signals for processing
                        try:
                            await self.signal_queue.put(buy_signal)
                            await self.signal_queue.put(sell_signal)
                        except asyncio.QueueFull:
                            logger.warning("⚠️ [HFT] Signal queue full, dropping market making signals")
                
                await asyncio.sleep(0.5)  # 500ms cycle for market making
                
        except Exception as e:
            logger.error(f"❌ [HFT] Market making engine error: {e}")

    async def _momentum_scalping_engine(self):
        """Momentum scalping strategy engine"""
        try:
            while True:
                if HFTStrategyType.MOMENTUM_SCALPING not in self.enabled_strategies:
                    await asyncio.sleep(1)
                    continue
                
                # Analyze price movements for momentum opportunities
                for feed_key, price_feed in self.price_feeds.items():
                    if len(price_feed) < 10:  # Need at least 10 data points
                        continue
                    
                    exchange_name, symbol = feed_key.split('_', 1)
                    
                    # Calculate short-term momentum
                    recent_prices = [p['price'] for p in list(price_feed)[-10:]]
                    price_change = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
                    price_change_bps = price_change * 10000
                    
                    # Calculate momentum strength
                    momentum_strength = abs(price_change_bps)
                    
                    # Check for strong momentum
                    if momentum_strength >= 10:  # 0.1% movement
                        side = 'BUY' if price_change > 0 else 'SELL'
                        confidence = min(0.9, momentum_strength / 50)  # Scale confidence
                        
                        signal = HFTSignal(
                            strategy_type=HFTStrategyType.MOMENTUM_SCALPING,
                            symbol=symbol,
                            side=side,
                            confidence=confidence,
                            urgency=0.8,  # High urgency for momentum
                            expected_profit_bps=momentum_strength * 0.3,
                            max_position_size=self.max_position_size * Decimal('0.2'),
                            time_horizon_ms=5000,  # 5 seconds
                            risk_score=0.4,
                            timestamp=time.time()
                        )
                        
                        try:
                            await self.signal_queue.put(signal)
                        except asyncio.QueueFull:
                            logger.warning("⚠️ [HFT] Signal queue full, dropping momentum signal")
                
                await asyncio.sleep(0.2)  # 200ms cycle for momentum scalping
                
        except Exception as e:
            logger.error(f"❌ [HFT] Momentum scalping engine error: {e}")

    async def _arbitrage_scalping_engine(self):
        """Arbitrage scalping strategy engine"""
        try:
            while True:
                if HFTStrategyType.ARBITRAGE_SCALPING not in self.enabled_strategies:
                    await asyncio.sleep(1)
                    continue
                
                # Look for arbitrage opportunities between exchanges
                symbols = set()
                for feed_key in self.price_feeds.keys():
                    _, symbol = feed_key.split('_', 1)
                    symbols.add(symbol)
                
                for symbol in symbols:
                    # Get prices from all exchanges for this symbol
                    exchange_prices = {}
                    for feed_key, price_feed in self.price_feeds.items():
                        if feed_key.endswith(f"_{symbol}") and price_feed:
                            exchange_name = feed_key.split('_')[0]
                            latest_price = price_feed[-1]['price']
                            exchange_prices[exchange_name] = latest_price
                    
                    if len(exchange_prices) >= 2:
                        # Find price differences
                        min_exchange = min(exchange_prices.keys(), key=lambda x: exchange_prices[x])
                        max_exchange = max(exchange_prices.keys(), key=lambda x: exchange_prices[x])
                        
                        min_price = exchange_prices[min_exchange]
                        max_price = exchange_prices[max_exchange]
                        
                        spread_bps = ((max_price - min_price) / min_price) * 10000
                        
                        # Check if arbitrage opportunity exists
                        if spread_bps >= self.min_profit_bps * 3:  # Need 3x min profit for fees
                            # Generate arbitrage signals
                            buy_signal = HFTSignal(
                                strategy_type=HFTStrategyType.ARBITRAGE_SCALPING,
                                symbol=symbol,
                                side='BUY',
                                confidence=0.9,
                                urgency=0.95,  # Very high urgency for arbitrage
                                expected_profit_bps=spread_bps * 0.7,  # Account for fees
                                max_position_size=self.max_position_size * Decimal('0.3'),
                                time_horizon_ms=2000,  # 2 seconds
                                risk_score=0.3,
                                timestamp=time.time()
                            )
                            
                            try:
                                await self.signal_queue.put(buy_signal)
                            except asyncio.QueueFull:
                                logger.warning("⚠️ [HFT] Signal queue full, dropping arbitrage signal")
                
                await asyncio.sleep(0.1)  # 100ms cycle for arbitrage scalping
                
        except Exception as e:
            logger.error(f"❌ [HFT] Arbitrage scalping engine error: {e}")

    async def _signal_processor(self):
        """Process and filter HFT signals"""
        try:
            while True:
                try:
                    # Get signal from queue with timeout
                    signal = await asyncio.wait_for(self.signal_queue.get(), timeout=1.0)

                    # Validate signal
                    if await self._validate_signal(signal):
                        # Add to execution queue
                        try:
                            await self.execution_queue.put(signal)
                        except asyncio.QueueFull:
                            logger.warning("⚠️ [HFT] Execution queue full, dropping signal")

                except asyncio.TimeoutError:
                    continue  # No signals to process

        except Exception as e:
            logger.error(f"❌ [HFT] Signal processor error: {e}")

    async def _validate_signal(self, signal: HFTSignal) -> bool:
        """Validate HFT signal before execution"""
        try:
            # Check signal age
            signal_age_ms = (time.time() - signal.timestamp) * 1000
            if signal_age_ms > self.max_latency_ms:
                logger.debug(f"🔍 [HFT] Signal too old: {signal_age_ms:.1f}ms")
                return False

            # Check confidence threshold
            if signal.confidence < 0.6:
                return False

            # Check expected profit
            if signal.expected_profit_bps < self.min_profit_bps:
                return False

            # Check position limits
            current_position = self.active_positions.get(signal.symbol, Decimal('0'))
            if abs(current_position + signal.max_position_size) > self.max_position_size:
                return False

            # Check risk limits
            if signal.risk_score > 0.7:
                return False

            return True

        except Exception as e:
            logger.error(f"❌ [HFT] Signal validation error: {e}")
            return False

    async def _execution_engine(self):
        """Execute HFT signals with ultra-low latency"""
        try:
            while True:
                try:
                    # Get signal from execution queue
                    signal = await asyncio.wait_for(self.execution_queue.get(), timeout=1.0)

                    # Execute signal
                    execution_start = time.time()
                    result = await self._execute_hft_signal(signal)
                    execution_time = (time.time() - execution_start) * 1000

                    # Track execution time
                    self.execution_times.append(execution_time)

                    # Update strategy performance
                    if result.get('success'):
                        self.strategy_performance[signal.strategy_type]['trades'] += 1
                        self.strategy_performance[signal.strategy_type]['avg_latency'] = np.mean(list(self.execution_times)[-100:])

                        logger.info(f"✅ [HFT] {signal.strategy_type.value} executed in {execution_time:.1f}ms")
                    else:
                        logger.warning(f"⚠️ [HFT] {signal.strategy_type.value} execution failed: {result.get('error')}")

                except asyncio.TimeoutError:
                    continue  # No signals to execute

        except Exception as e:
            logger.error(f"❌ [HFT] Execution engine error: {e}")

    async def _execute_hft_signal(self, signal: HFTSignal) -> Dict:
        """Execute a single HFT signal"""
        try:
            # Find best exchange for execution
            best_exchange = await self._find_best_execution_exchange(signal)
            if not best_exchange:
                return {'success': False, 'error': 'No suitable exchange found'}

            exchange = self.exchange_manager.exchanges[best_exchange]

            # Calculate position size based on urgency and confidence
            position_size = signal.max_position_size * Decimal(str(signal.confidence * signal.urgency))
            position_size = max(position_size, Decimal('10'))  # Minimum position size

            # Execute order
            order_result = await exchange.place_order(
                symbol=signal.symbol,
                side=signal.side,
                order_type='MARKET',  # Market orders for speed
                quantity=position_size
            )

            if order_result.get('order_id'):
                # Update position tracking
                current_position = self.active_positions.get(signal.symbol, Decimal('0'))
                if signal.side == 'BUY':
                    self.active_positions[signal.symbol] = current_position + position_size
                else:
                    self.active_positions[signal.symbol] = current_position - position_size

                return {
                    'success': True,
                    'order_id': order_result['order_id'],
                    'symbol': signal.symbol,
                    'side': signal.side,
                    'quantity': position_size,
                    'strategy': signal.strategy_type.value
                }
            else:
                return {'success': False, 'error': 'Order placement failed'}

        except Exception as e:
            logger.error(f"❌ [HFT] Signal execution error: {e}")
            return {'success': False, 'error': str(e)}

    async def _find_best_execution_exchange(self, signal: HFTSignal) -> Optional[str]:
        """Find the best exchange for executing a signal"""
        try:
            # Get active exchanges
            active_exchanges = [name for name, status in self.exchange_manager.exchange_status.items()
                              if status.active]

            if not active_exchanges:
                return None

            # Score exchanges based on latency and liquidity
            exchange_scores = {}
            for exchange_name in active_exchanges:
                status = self.exchange_manager.exchange_status[exchange_name]

                # Calculate score based on latency and liquidity
                latency_score = max(0, 1 - (status.latency_ms / self.max_latency_ms))
                liquidity_score = status.liquidity_score

                exchange_scores[exchange_name] = (latency_score * 0.6) + (liquidity_score * 0.4)

            # Return exchange with highest score
            if exchange_scores:
                return max(exchange_scores.keys(), key=lambda x: exchange_scores[x])

            return active_exchanges[0]  # Fallback to first active exchange

        except Exception as e:
            logger.error(f"❌ [HFT] Error finding best exchange: {e}")
            return None

    async def _performance_monitor(self):
        """Monitor HFT performance and adjust strategies"""
        try:
            while True:
                await asyncio.sleep(60)  # Monitor every minute

                # Calculate performance metrics
                avg_latency = np.mean(list(self.latency_history)) if self.latency_history else 0
                avg_execution_time = np.mean(list(self.execution_times)) if self.execution_times else 0

                # Log performance
                logger.info(f"📊 [HFT] Avg Latency: {avg_latency:.1f}ms, Avg Execution: {avg_execution_time:.1f}ms")

                # Check if performance is within targets
                if avg_latency > self.target_latency_ms:
                    logger.warning(f"⚠️ [HFT] Latency above target: {avg_latency:.1f}ms > {self.target_latency_ms}ms")

                if avg_execution_time > self.target_latency_ms:
                    logger.warning(f"⚠️ [HFT] Execution time above target: {avg_execution_time:.1f}ms")

                # Log strategy performance
                for strategy_type, performance in self.strategy_performance.items():
                    if performance['trades'] > 0:
                        logger.info(f"📈 [HFT] {strategy_type.value}: {performance['trades']} trades, "
                                  f"${performance['profit']:.2f} profit")

        except Exception as e:
            logger.error(f"❌ [HFT] Performance monitor error: {e}")

    def get_hft_status(self) -> Dict:
        """Get current HFT status and performance"""
        try:
            avg_latency = np.mean(list(self.latency_history)) if self.latency_history else 0
            avg_execution_time = np.mean(list(self.execution_times)) if self.execution_times else 0

            return {
                'enabled_strategies': [s.value for s in self.enabled_strategies],
                'avg_latency_ms': round(avg_latency, 2),
                'avg_execution_time_ms': round(avg_execution_time, 2),
                'signal_queue_size': self.signal_queue.qsize(),
                'execution_queue_size': self.execution_queue.qsize(),
                'active_positions': {k: float(v) for k, v in self.active_positions.items()},
                'strategy_performance': {
                    k.value if hasattr(k, 'value') else str(k): {
                        'trades': v['trades'],
                        'profit': float(v['profit']),
                        'avg_latency': v['avg_latency']
                    } for k, v in self.strategy_performance.items()
                },
                'performance_within_targets': {
                    'latency': avg_latency <= self.target_latency_ms,
                    'execution': avg_execution_time <= self.target_latency_ms
                }
            }

        except Exception as e:
            logger.error(f"❌ [HFT] Error getting status: {e}")
            return {'error': str(e)}
