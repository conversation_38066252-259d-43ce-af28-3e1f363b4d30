"""
Dynamic Portfolio Rebalancing System

Advanced portfolio rebalancing engine that automatically redistributes holdings
across different cryptocurrencies to maintain optimal trading capacity and
prevent single-currency depletion. Includes risk-based allocation and
volatility-adjusted rebalancing based on institutional portfolio management strategies.
"""

import asyncio
import logging
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import time
import math
import numpy as np

logger = logging.getLogger(__name__)

class RebalancingStrategy(Enum):
    """Portfolio rebalancing strategies"""
    EQUAL_WEIGHT = "equal_weight"           # Equal allocation across all currencies
    RISK_PARITY = "risk_parity"            # Risk-adjusted allocation
    MOMENTUM = "momentum"                   # Momentum-based allocation
    MEAN_REVERSION = "mean_reversion"      # Mean reversion targeting
    VOLATILITY_ADJUSTED = "volatility_adjusted"  # Volatility-based allocation
    MARKET_CAP_WEIGHTED = "market_cap_weighted"   # Market cap weighted

@dataclass
class AllocationTarget:
    """Target allocation for a currency"""
    currency: str
    target_percentage: Decimal
    current_percentage: Decimal
    deviation: Decimal
    priority: int
    risk_score: float
    volatility: float
    momentum_score: float

@dataclass
class RebalancingAction:
    """Action to rebalance portfolio"""
    currency_from: str
    currency_to: str
    amount: Decimal
    percentage: Decimal
    reason: str
    priority: int
    expected_impact: Decimal
    risk_adjustment: float

class DynamicPortfolioRebalancer:
    """
    Advanced portfolio rebalancing system implementing institutional-grade
    portfolio management strategies with risk-based allocation and
    volatility-adjusted rebalancing
    """
    
    def __init__(self, exchange_clients: Dict[str, Any], config: Dict = None):
        self.exchange_clients = exchange_clients
        self.config = config or {}
        
        # Rebalancing configuration
        self.rebalancing_strategy = RebalancingStrategy(
            self.config.get('strategy', 'volatility_adjusted')
        )
        self.rebalancing_threshold = Decimal(str(self.config.get('threshold', 0.05)))  # 5%
        self.min_rebalancing_amount = Decimal(str(self.config.get('min_amount', 10.0)))
        self.max_single_rebalance = Decimal(str(self.config.get('max_single', 0.2)))  # 20%
        
        # Risk management
        self.max_currency_allocation = Decimal(str(self.config.get('max_allocation', 0.4)))  # 40%
        self.min_currency_allocation = Decimal(str(self.config.get('min_allocation', 0.05)))  # 5%
        self.volatility_lookback_days = self.config.get('volatility_days', 30)
        
        # Portfolio state
        self.current_allocations = {}
        self.target_allocations = {}
        self.price_history = {}
        self.volatility_data = {}
        self.correlation_matrix = {}
        
        # Performance tracking
        self.rebalancing_history = []
        self.performance_metrics = {}
        
        logger.info("📊 [REBALANCER] Initialized dynamic portfolio rebalancer")
    
    async def initialize(self):
        """Initialize the portfolio rebalancer"""
        try:
            logger.info("🔧 [REBALANCER] Initializing portfolio rebalancer...")
            
            # Get current portfolio state
            await self.update_current_allocations()
            
            # Calculate historical volatilities
            await self.calculate_volatilities()
            
            # Calculate currency correlations
            await self.calculate_correlations()
            
            # Set initial target allocations
            await self.calculate_target_allocations()
            
            logger.info("✅ [REBALANCER] Portfolio rebalancer initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ [REBALANCER] Error initializing rebalancer: {e}")
            raise
    
    async def update_current_allocations(self):
        """Update current portfolio allocations"""
        try:
            logger.info("📊 [REBALANCER] Updating current portfolio allocations...")
            
            total_portfolio_value = Decimal('0')
            currency_values = {}
            
            # Get balances from all exchanges
            for exchange_name, client in self.exchange_clients.items():
                try:
                    if hasattr(client, 'get_all_available_balances'):
                        balances = await client.get_all_available_balances()
                        
                        for currency, balance in balances.items():
                            if balance > 0:
                                # Get USD value
                                usd_value = await self._get_usd_value(currency, balance, exchange_name)
                                if usd_value > 0:
                                    currency_values[currency] = currency_values.get(currency, Decimal('0')) + usd_value
                                    total_portfolio_value += usd_value
                
                except Exception as e:
                    logger.warning(f"⚠️ [REBALANCER] Error getting balances from {exchange_name}: {e}")
                    continue
            
            # Calculate current allocations as percentages
            self.current_allocations = {}
            if total_portfolio_value > 0:
                for currency, value in currency_values.items():
                    percentage = value / total_portfolio_value
                    self.current_allocations[currency] = {
                        'value': value,
                        'percentage': percentage,
                        'last_update': time.time()
                    }
            
            logger.info(f"📊 [REBALANCER] Portfolio value: ${total_portfolio_value:.2f}")
            logger.info(f"📊 [REBALANCER] Currencies: {len(self.current_allocations)}")
            
            # Log current allocations
            for currency, data in self.current_allocations.items():
                logger.info(f"📊 [REBALANCER] {currency}: {data['percentage']*100:.2f}% (${data['value']:.2f})")
            
        except Exception as e:
            logger.error(f"❌ [REBALANCER] Error updating current allocations: {e}")
    
    async def calculate_volatilities(self):
        """Calculate historical volatilities for each currency"""
        try:
            logger.info("📈 [REBALANCER] Calculating currency volatilities...")
            
            # For now, use simplified volatility estimates
            # In production, this would use historical price data
            default_volatilities = {
                'BTC': 0.6,    # 60% annual volatility
                'ETH': 0.8,    # 80% annual volatility
                'SOL': 1.2,    # 120% annual volatility
                'ADA': 1.0,    # 100% annual volatility
                'DOT': 1.1,    # 110% annual volatility
                'LINK': 1.0,   # 100% annual volatility
                'UNI': 1.3,    # 130% annual volatility
                'AVAX': 1.4,   # 140% annual volatility
                'MATIC': 1.1,  # 110% annual volatility
                'USDT': 0.02,  # 2% annual volatility (stablecoin)
                'USD': 0.01,   # 1% annual volatility
                'USDC': 0.02   # 2% annual volatility (stablecoin)
            }
            
            self.volatility_data = {}
            for currency in self.current_allocations.keys():
                volatility = default_volatilities.get(currency, 1.0)  # Default 100%
                self.volatility_data[currency] = {
                    'annual_volatility': volatility,
                    'daily_volatility': volatility / math.sqrt(365),
                    'risk_score': min(volatility / 0.5, 2.0),  # Normalize to 0-2 scale
                    'last_update': time.time()
                }
            
            logger.info(f"📈 [REBALANCER] Calculated volatilities for {len(self.volatility_data)} currencies")
            
        except Exception as e:
            logger.error(f"❌ [REBALANCER] Error calculating volatilities: {e}")
    
    async def calculate_correlations(self):
        """Calculate currency correlations for risk management"""
        try:
            logger.info("🔗 [REBALANCER] Calculating currency correlations...")
            
            # Simplified correlation matrix for major cryptocurrencies
            # In production, this would be calculated from historical price data
            default_correlations = {
                ('BTC', 'ETH'): 0.7,
                ('BTC', 'SOL'): 0.6,
                ('BTC', 'ADA'): 0.5,
                ('BTC', 'DOT'): 0.5,
                ('ETH', 'SOL'): 0.8,
                ('ETH', 'ADA'): 0.6,
                ('ETH', 'DOT'): 0.6,
                ('SOL', 'ADA'): 0.5,
                ('SOL', 'DOT'): 0.5,
                ('ADA', 'DOT'): 0.7,
                # Stablecoins have low correlation with crypto
                ('USDT', 'BTC'): 0.1,
                ('USDT', 'ETH'): 0.1,
                ('USD', 'BTC'): 0.1,
                ('USDC', 'BTC'): 0.1,
            }
            
            self.correlation_matrix = {}
            currencies = list(self.current_allocations.keys())
            
            for i, curr1 in enumerate(currencies):
                for j, curr2 in enumerate(currencies):
                    if i == j:
                        correlation = 1.0  # Perfect correlation with itself
                    else:
                        # Look up correlation in both directions
                        correlation = default_correlations.get((curr1, curr2), 
                                    default_correlations.get((curr2, curr1), 0.3))  # Default 30%
                    
                    self.correlation_matrix[(curr1, curr2)] = correlation
            
            logger.info(f"🔗 [REBALANCER] Built correlation matrix for {len(currencies)} currencies")
            
        except Exception as e:
            logger.error(f"❌ [REBALANCER] Error calculating correlations: {e}")
    
    async def calculate_target_allocations(self) -> Dict[str, AllocationTarget]:
        """Calculate target allocations based on selected strategy"""
        try:
            logger.info(f"🎯 [REBALANCER] Calculating target allocations using {self.rebalancing_strategy.value}")
            
            currencies = list(self.current_allocations.keys())
            target_allocations = {}
            
            if self.rebalancing_strategy == RebalancingStrategy.EQUAL_WEIGHT:
                target_allocations = await self._calculate_equal_weight_targets(currencies)
            elif self.rebalancing_strategy == RebalancingStrategy.RISK_PARITY:
                target_allocations = await self._calculate_risk_parity_targets(currencies)
            elif self.rebalancing_strategy == RebalancingStrategy.VOLATILITY_ADJUSTED:
                target_allocations = await self._calculate_volatility_adjusted_targets(currencies)
            elif self.rebalancing_strategy == RebalancingStrategy.MOMENTUM:
                target_allocations = await self._calculate_momentum_targets(currencies)
            else:
                # Default to volatility-adjusted
                target_allocations = await self._calculate_volatility_adjusted_targets(currencies)
            
            self.target_allocations = target_allocations
            
            # Log target allocations
            for currency, target in target_allocations.items():
                current_pct = self.current_allocations[currency]['percentage'] * 100
                target_pct = target.target_percentage * 100
                deviation = target.deviation * 100
                
                logger.info(f"🎯 [REBALANCER] {currency}: {current_pct:.2f}% -> {target_pct:.2f}% (deviation: {deviation:+.2f}%)")
            
            return target_allocations
            
        except Exception as e:
            logger.error(f"❌ [REBALANCER] Error calculating target allocations: {e}")
            return {}
    
    async def _calculate_equal_weight_targets(self, currencies: List[str]) -> Dict[str, AllocationTarget]:
        """Calculate equal weight target allocations"""
        targets = {}
        target_percentage = Decimal('1') / Decimal(str(len(currencies)))
        
        for currency in currencies:
            current_percentage = self.current_allocations[currency]['percentage']
            deviation = target_percentage - current_percentage
            
            targets[currency] = AllocationTarget(
                currency=currency,
                target_percentage=target_percentage,
                current_percentage=current_percentage,
                deviation=deviation,
                priority=1,
                risk_score=self.volatility_data.get(currency, {}).get('risk_score', 1.0),
                volatility=self.volatility_data.get(currency, {}).get('annual_volatility', 1.0),
                momentum_score=0.0
            )
        
        return targets
    
    async def _calculate_risk_parity_targets(self, currencies: List[str]) -> Dict[str, AllocationTarget]:
        """Calculate risk parity target allocations"""
        targets = {}
        
        # Calculate inverse volatility weights
        total_inv_vol = Decimal('0')
        inv_volatilities = {}
        
        for currency in currencies:
            volatility = self.volatility_data.get(currency, {}).get('annual_volatility', 1.0)
            inv_vol = Decimal('1') / Decimal(str(max(volatility, 0.01)))  # Avoid division by zero
            inv_volatilities[currency] = inv_vol
            total_inv_vol += inv_vol
        
        # Calculate target percentages
        for currency in currencies:
            target_percentage = inv_volatilities[currency] / total_inv_vol
            current_percentage = self.current_allocations[currency]['percentage']
            deviation = target_percentage - current_percentage
            
            targets[currency] = AllocationTarget(
                currency=currency,
                target_percentage=target_percentage,
                current_percentage=current_percentage,
                deviation=deviation,
                priority=2,
                risk_score=self.volatility_data.get(currency, {}).get('risk_score', 1.0),
                volatility=self.volatility_data.get(currency, {}).get('annual_volatility', 1.0),
                momentum_score=0.0
            )
        
        return targets
    
    async def _calculate_volatility_adjusted_targets(self, currencies: List[str]) -> Dict[str, AllocationTarget]:
        """Enhanced volatility-adjusted target allocations with risk-return optimization"""
        targets = {}

        # Enhanced asset classification
        stablecoins = ['USDT', 'USD', 'USDC', 'DAI', 'BUSD']
        major_crypto = ['BTC', 'ETH']  # Blue chip crypto
        alt_crypto = [c for c in currencies if c not in stablecoins and c not in major_crypto]

        stable_currencies = [c for c in currencies if c in stablecoins]
        major_currencies = [c for c in currencies if c in major_crypto]
        alt_currencies = [c for c in currencies if c in alt_crypto]

        # Dynamic allocation based on market conditions
        market_volatility = await self._calculate_market_volatility()

        # Adjust allocations based on market conditions
        if market_volatility > 0.6:  # High volatility - more conservative
            stable_allocation = Decimal('0.4')  # 40% stable
            major_allocation = Decimal('0.45')  # 45% major crypto
            alt_allocation = Decimal('0.15')    # 15% alt crypto
        elif market_volatility < 0.3:  # Low volatility - more aggressive
            stable_allocation = Decimal('0.15') # 15% stable
            major_allocation = Decimal('0.5')   # 50% major crypto
            alt_allocation = Decimal('0.35')    # 35% alt crypto
        else:  # Medium volatility - balanced
            stable_allocation = Decimal('0.25') # 25% stable
            major_allocation = Decimal('0.5')   # 50% major crypto
            alt_allocation = Decimal('0.25')    # 25% alt crypto

        # Calculate risk-adjusted weights for each category
        stable_weights = await self._calculate_risk_adjusted_weights(stable_currencies)
        major_weights = await self._calculate_risk_adjusted_weights(major_currencies)
        alt_weights = await self._calculate_risk_adjusted_weights(alt_currencies)

        # Set targets for each currency
        for currency in currencies:
            if currency in stable_currencies:
                if stable_weights and currency in stable_weights:
                    target_percentage = stable_allocation * stable_weights[currency]
                else:
                    target_percentage = stable_allocation / Decimal(str(max(len(stable_currencies), 1)))
            elif currency in major_currencies:
                if major_weights and currency in major_weights:
                    target_percentage = major_allocation * major_weights[currency]
                else:
                    target_percentage = major_allocation / Decimal(str(max(len(major_currencies), 1)))
            else:  # alt currencies
                if alt_weights and currency in alt_weights:
                    target_percentage = alt_allocation * alt_weights[currency]
                else:
                    target_percentage = alt_allocation / Decimal(str(max(len(alt_currencies), 1)))

            # Apply min/max constraints
            target_percentage = max(self.min_currency_allocation,
                                  min(self.max_currency_allocation, target_percentage))

            current_percentage = self.current_allocations[currency]['percentage']
            deviation = target_percentage - current_percentage

            # Calculate priority based on deviation and asset class
            if currency in major_currencies:
                base_priority = 1  # Highest priority for major crypto
            elif currency in stable_currencies:
                base_priority = 2  # Medium priority for stablecoins
            else:
                base_priority = 3  # Lower priority for alt crypto

            priority = base_priority + int(abs(deviation) * 10)

            targets[currency] = AllocationTarget(
                currency=currency,
                target_percentage=target_percentage,
                current_percentage=current_percentage,
                deviation=deviation,
                priority=priority,
                risk_score=self.volatility_data.get(currency, {}).get('risk_score', 1.0),
                volatility=self.volatility_data.get(currency, {}).get('annual_volatility', 1.0),
                momentum_score=self.volatility_data.get(currency, {}).get('momentum_score', 0.0)
            )

        return targets

    async def _calculate_market_volatility(self) -> float:
        """Calculate overall market volatility"""
        try:
            total_volatility = 0.0
            count = 0

            for currency, data in self.volatility_data.items():
                if currency != 'USDT':  # Exclude stablecoins
                    volatility = data.get('annual_volatility', 1.0)
                    total_volatility += volatility
                    count += 1

            if count > 0:
                avg_volatility = total_volatility / count
                return min(avg_volatility, 2.0)  # Cap at 200%

            return 0.5  # Default medium volatility

        except Exception as e:
            logger.error(f"❌ [REBALANCER] Error calculating market volatility: {e}")
            return 0.5

    async def _calculate_risk_adjusted_weights(self, currencies: List[str]) -> Dict[str, Decimal]:
        """Calculate risk-adjusted weights for a group of currencies"""
        try:
            if not currencies:
                return {}

            weights = {}
            total_score = Decimal('0')

            # Calculate risk-adjusted scores
            for currency in currencies:
                volatility_data = self.volatility_data.get(currency, {})

                # Get risk factors
                annual_volatility = volatility_data.get('annual_volatility', 1.0)
                max_drawdown = volatility_data.get('max_drawdown', 0.5)
                sharpe_ratio = volatility_data.get('sharpe_ratio', 0.0)
                momentum_score = volatility_data.get('momentum_score', 0.0)

                # Calculate composite score (higher is better)
                volatility_score = 1.0 / max(annual_volatility, 0.05)  # Inverse volatility
                drawdown_score = 1.0 / max(max_drawdown, 0.01)  # Inverse drawdown
                sharpe_score = max(sharpe_ratio + 1.0, 0.1)  # Adjusted Sharpe
                momentum_score = max(momentum_score + 1.0, 0.1)  # Adjusted momentum

                # Weighted composite score
                composite_score = Decimal(str(
                    volatility_score * 0.3 +  # 30% volatility
                    drawdown_score * 0.3 +    # 30% drawdown
                    sharpe_score * 0.25 +     # 25% Sharpe
                    momentum_score * 0.15     # 15% momentum
                ))

                weights[currency] = composite_score
                total_score += composite_score

            # Normalize weights to sum to 1
            if total_score > 0:
                for currency in currencies:
                    weights[currency] = weights[currency] / total_score
            else:
                # Equal weights if no valid data
                equal_weight = Decimal('1') / Decimal(str(len(currencies)))
                for currency in currencies:
                    weights[currency] = equal_weight

            return weights

        except Exception as e:
            logger.error(f"❌ [REBALANCER] Error calculating risk-adjusted weights: {e}")
            return {}
        
        # Set targets
        for currency in currencies:
            if currency in stable_currencies:
                target_percentage = stable_target
            else:
                if crypto_currencies and total_inv_vol > 0:
                    crypto_weight = crypto_inv_volatilities[currency] / total_inv_vol
                    target_percentage = crypto_allocation * crypto_weight
                else:
                    target_percentage = Decimal('0')
            
            # Apply min/max constraints
            target_percentage = max(self.min_currency_allocation, 
                                  min(self.max_currency_allocation, target_percentage))
            
            current_percentage = self.current_allocations[currency]['percentage']
            deviation = target_percentage - current_percentage
            
            targets[currency] = AllocationTarget(
                currency=currency,
                target_percentage=target_percentage,
                current_percentage=current_percentage,
                deviation=deviation,
                priority=1 if currency in stable_currencies else 2,
                risk_score=self.volatility_data.get(currency, {}).get('risk_score', 1.0),
                volatility=self.volatility_data.get(currency, {}).get('annual_volatility', 1.0),
                momentum_score=0.0
            )
        
        return targets
    
    async def _calculate_momentum_targets(self, currencies: List[str]) -> Dict[str, AllocationTarget]:
        """Calculate momentum-based target allocations"""
        # For now, use equal weight as momentum calculation requires price history
        return await self._calculate_equal_weight_targets(currencies)
    
    async def _get_usd_value(self, currency: str, amount: float, exchange_name: str) -> Decimal:
        """Get USD value of a currency amount"""
        try:
            if currency in ['USD', 'USDT', 'USDC']:
                return Decimal(str(amount))

            # Try to get price from exchange
            client = self.exchange_clients[exchange_name]
            symbol = f"{currency}USDT"

            if hasattr(client, 'get_price'):
                price = client.get_price(symbol)
                if price and float(price) > 0:
                    return Decimal(str(amount)) * Decimal(str(price))

            return Decimal('0')

        except Exception as e:
            logger.debug(f"Error getting USD value for {currency}: {e}")
            return Decimal('0')

    async def identify_rebalancing_needs(self) -> List[RebalancingAction]:
        """Identify currencies that need rebalancing"""
        try:
            logger.info("🔍 [REBALANCER] Identifying rebalancing needs...")

            # Update current state
            await self.update_current_allocations()
            await self.calculate_target_allocations()

            rebalancing_actions = []

            # Find currencies that deviate significantly from targets
            for currency, target in self.target_allocations.items():
                deviation_abs = abs(target.deviation)

                if deviation_abs > self.rebalancing_threshold:
                    # Determine if we need to buy or sell this currency
                    if target.deviation > 0:
                        # Need to increase allocation (buy)
                        action_type = "buy"
                        priority = 1 if target.priority == 1 else 2
                    else:
                        # Need to decrease allocation (sell)
                        action_type = "sell"
                        priority = 2 if target.priority == 1 else 1

                    # Calculate rebalancing amount with safety checks
                    total_portfolio_value = sum(
                        data['value'] for data in self.current_allocations.values()
                    )

                    # CRITICAL FIX: Validate portfolio value to prevent massive calculations
                    if total_portfolio_value > 10000:  # Safety check for corrupted portfolio data
                        logger.error(f"❌ [REBALANCER] Suspicious portfolio value: ${total_portfolio_value:.2f} - capping at $1000")
                        total_portfolio_value = 1000.0  # Cap at $1000 for safety
                    elif total_portfolio_value <= 0:
                        logger.warning(f"⚠️ [REBALANCER] Invalid portfolio value: ${total_portfolio_value:.2f} - skipping rebalancing")
                        continue

                    # Calculate rebalance amount with precision control
                    rebalance_amount = Decimal(str(deviation_abs)) * Decimal(str(total_portfolio_value))

                    # CRITICAL FIX: Cap maximum single rebalance amount
                    max_single_rebalance = Decimal(str(total_portfolio_value)) * self.max_single_rebalance
                    if rebalance_amount > max_single_rebalance:
                        logger.warning(f"⚠️ [REBALANCER] Rebalance amount ${rebalance_amount:.2f} exceeds maximum ${max_single_rebalance:.2f} - capping")
                        rebalance_amount = max_single_rebalance

                    # Additional safety: Cap at $200 per rebalance for live trading
                    if rebalance_amount > Decimal('200'):
                        logger.warning(f"⚠️ [REBALANCER] Rebalance amount ${rebalance_amount:.2f} exceeds safety limit - capping at $200")
                        rebalance_amount = Decimal('200')  # CRITICAL FIX: Increased from $100 to $200 for better opportunities

                    # Only proceed if amount is above minimum and below maximum
                    if rebalance_amount >= self.min_rebalancing_amount and rebalance_amount <= Decimal('100'):
                        # Find best counterpart currency
                        counterpart = await self._find_best_counterpart(currency, action_type)

                        if counterpart:
                            if action_type == "buy":
                                currency_from = counterpart
                                currency_to = currency
                            else:
                                currency_from = currency
                                currency_to = counterpart

                            action = RebalancingAction(
                                currency_from=currency_from,
                                currency_to=currency_to,
                                amount=rebalance_amount,
                                percentage=deviation_abs,
                                reason=f"Deviation {deviation_abs*100:.2f}% > threshold {self.rebalancing_threshold*100:.2f}%",
                                priority=priority,
                                expected_impact=deviation_abs,
                                risk_adjustment=target.risk_score
                            )

                            rebalancing_actions.append(action)

                            logger.info(f"🔍 [REBALANCER] Action needed: {currency_from} -> {currency_to}")
                            logger.info(f"🔍 [REBALANCER] Amount: ${rebalance_amount:.2f} ({deviation_abs*100:.2f}%)")

            # Sort by priority and expected impact
            rebalancing_actions.sort(key=lambda x: (x.priority, -float(x.expected_impact)))

            logger.info(f"🔍 [REBALANCER] Identified {len(rebalancing_actions)} rebalancing actions")

            return rebalancing_actions

        except Exception as e:
            logger.error(f"❌ [REBALANCER] Error identifying rebalancing needs: {e}")
            return []

    async def _find_best_counterpart(self, currency: str, action_type: str) -> Optional[str]:
        """Find the best counterpart currency for rebalancing"""
        try:
            candidates = []

            for other_currency, target in self.target_allocations.items():
                if other_currency == currency:
                    continue

                # For buy actions, look for currencies with negative deviation (over-allocated)
                # For sell actions, look for currencies with positive deviation (under-allocated)
                if action_type == "buy" and target.deviation < 0:
                    candidates.append((other_currency, abs(target.deviation), target.risk_score))
                elif action_type == "sell" and target.deviation > 0:
                    candidates.append((other_currency, abs(target.deviation), target.risk_score))

            if not candidates:
                return None

            # Sort by deviation (descending) and risk score (ascending)
            candidates.sort(key=lambda x: (-x[1], x[2]))

            return candidates[0][0]

        except Exception as e:
            logger.error(f"❌ [REBALANCER] Error finding counterpart: {e}")
            return None

    async def execute_rebalancing_action(self, action: RebalancingAction) -> Dict[str, Any]:
        """Execute a rebalancing action"""
        try:
            logger.info(f"⚡ [REBALANCER] Executing rebalancing action")
            logger.info(f"⚡ [REBALANCER] {action.currency_from} -> {action.currency_to}: ${action.amount:.2f}")

            # Find suitable exchange for execution
            exchange_name = await self._find_best_exchange_for_rebalancing(action)
            if not exchange_name:
                return {"success": False, "error": "No suitable exchange found"}

            client = self.exchange_clients[exchange_name]

            # Determine trading symbol and side
            symbol, side = await self._determine_trading_parameters(action, exchange_name)
            if not symbol:
                return {"success": False, "error": "Could not determine trading parameters"}

            # Calculate trade amount
            trade_amount = await self._calculate_trade_amount(action, symbol, side, exchange_name)
            if trade_amount <= 0:
                return {"success": False, "error": "Invalid trade amount"}

            # Execute the trade
            if hasattr(client, 'place_order'):
                result = await client.place_order(
                    symbol=symbol,
                    side=side,
                    amount=float(trade_amount),
                    order_type='market'
                )

                if 'error' not in result:
                    # Record successful rebalancing
                    self.rebalancing_history.append({
                        'timestamp': time.time(),
                        'action': action,
                        'result': result,
                        'exchange': exchange_name
                    })

                    logger.info(f"✅ [REBALANCER] Successfully executed rebalancing")
                    return {"success": True, "order_id": result.get('order_id'), "result": result}
                else:
                    logger.error(f"❌ [REBALANCER] Order failed: {result['error']}")
                    return {"success": False, "error": result['error']}
            else:
                return {"success": False, "error": "Client does not support place_order"}

        except Exception as e:
            logger.error(f"❌ [REBALANCER] Error executing rebalancing action: {e}")
            return {"success": False, "error": str(e)}

    async def _find_best_exchange_for_rebalancing(self, action: RebalancingAction) -> Optional[str]:
        """Find the best exchange for executing a rebalancing action"""
        try:
            # For now, use the first available exchange
            # In production, this would consider liquidity, fees, and availability
            for exchange_name, client in self.exchange_clients.items():
                if hasattr(client, 'place_order'):
                    return exchange_name
            return None

        except Exception as e:
            logger.error(f"❌ [REBALANCER] Error finding exchange: {e}")
            return None

    async def _determine_trading_parameters(self, action: RebalancingAction, exchange_name: str) -> Tuple[Optional[str], Optional[str]]:
        """Determine trading symbol and side for rebalancing action using USDT as intermediary"""
        try:
            client = self.exchange_clients[exchange_name]

            # CRITICAL FIX: Always use USDT as intermediary for rebalancing
            # This prevents invalid symbols like BTCDOT, BTCSOL, BTCADA

            # Strategy: Convert currency_from -> USDT, then USDT -> currency_to
            # For this implementation, we'll handle the first step (currency_from -> USDT)
            # The second step will be handled in a separate transaction

            # Step 1: Sell currency_from for USDT (if not already USDT)
            if action.currency_from != 'USDT':
                symbol = f"{action.currency_from}USDT"
                side = "sell"

                # Validate this symbol exists on the exchange
                if hasattr(client, '_is_valid_bybit_symbol'):
                    try:
                        if client._is_valid_bybit_symbol(symbol):
                            logger.info(f"✅ [REBALANCER] Using valid symbol: {symbol} (side: {side})")
                            return symbol, side
                        else:
                            logger.warning(f"⚠️ [REBALANCER] Invalid symbol rejected: {symbol}")
                    except Exception as e:
                        logger.debug(f"Symbol validation error for {symbol}: {e}")

                # Fallback: Try to get price to validate
                try:
                    price = client.get_price(symbol)
                    if price and float(price) > 0:
                        logger.info(f"✅ [REBALANCER] Price validation successful for {symbol}: ${price}")
                        return symbol, side
                except Exception as e:
                    logger.warning(f"⚠️ [REBALANCER] Price validation failed for {symbol}: {e}")

            # Step 2: If currency_from is USDT, buy currency_to with USDT
            elif action.currency_to != 'USDT':
                symbol = f"{action.currency_to}USDT"
                side = "buy"

                # Validate this symbol exists on the exchange
                if hasattr(client, '_is_valid_bybit_symbol'):
                    try:
                        if client._is_valid_bybit_symbol(symbol):
                            logger.info(f"✅ [REBALANCER] Using valid symbol: {symbol} (side: {side})")
                            return symbol, side
                        else:
                            logger.warning(f"⚠️ [REBALANCER] Invalid symbol rejected: {symbol}")
                    except Exception as e:
                        logger.debug(f"Symbol validation error for {symbol}: {e}")

                # Fallback: Try to get price to validate
                try:
                    price = client.get_price(symbol)
                    if price and float(price) > 0:
                        logger.info(f"✅ [REBALANCER] Price validation successful for {symbol}: ${price}")
                        return symbol, side
                except Exception as e:
                    logger.warning(f"⚠️ [REBALANCER] Price validation failed for {symbol}: {e}")

            # If we reach here, no valid trading pair was found
            logger.error(f"❌ [REBALANCER] No valid trading pair found for {action.currency_from} -> {action.currency_to}")
            return None, None

        except Exception as e:
            logger.error(f"❌ [REBALANCER] Error determining trading parameters: {e}")
            return None, None

    async def _calculate_trade_amount(self, action: RebalancingAction, symbol: str, side: str, exchange_name: str) -> Decimal:
        """Calculate the appropriate trade amount for the rebalancing action"""
        try:
            client = self.exchange_clients[exchange_name]

            # Get current price for the symbol
            try:
                price = client.get_price(symbol)
                current_price = Decimal(str(price)) if price and float(price) > 0 else Decimal('0')
            except Exception as e:
                logger.warning(f"⚠️ [REBALANCER] Could not get price for {symbol}: {e}")
                return Decimal('0')

            if current_price <= 0:
                logger.warning(f"⚠️ [REBALANCER] Invalid price for {symbol}: {current_price}")
                return Decimal('0')

            # CRITICAL FIX: Ensure action.amount is a proper Decimal with reasonable precision
            action_amount = Decimal(str(action.amount))

            # Validate action amount to prevent massive calculations
            if action_amount > Decimal('200'):
                logger.error(f"❌ [REBALANCER] Action amount ${action_amount:.2f} exceeds safety limit - capping at $200")
                action_amount = Decimal('200')  # CRITICAL FIX: Increased from $100 to $200 for better opportunities
            elif action_amount <= Decimal('0'):
                logger.warning(f"⚠️ [REBALANCER] Invalid action amount: ${action_amount:.2f}")
                return Decimal('0')

            # Calculate trade amount based on side with proper decimal handling
            if side == "sell":
                # Selling currency_from, amount is in base currency
                trade_amount = action_amount / current_price
            else:
                # Buying currency_to, amount is in quote currency (USDT)
                trade_amount = action_amount / current_price

            # CRITICAL FIX: Round to reasonable precision to prevent extreme decimal values
            # For crypto trading, 8 decimal places is typically sufficient
            trade_amount = trade_amount.quantize(Decimal('0.00000001'))

            # Apply minimum and maximum trade amount constraints
            min_trade_amount = Decimal('0.001')  # Minimum trade amount
            max_trade_amount = Decimal('50')     # CRITICAL FIX: Increased from $10 to $50 for better trading opportunities

            if trade_amount < min_trade_amount:
                logger.warning(f"⚠️ [REBALANCER] Trade amount {trade_amount} below minimum {min_trade_amount}")
                return Decimal('0')
            elif trade_amount > max_trade_amount:
                logger.warning(f"⚠️ [REBALANCER] Trade amount {trade_amount} exceeds maximum {max_trade_amount} - capping")
                trade_amount = max_trade_amount

            logger.info(f"💰 [REBALANCER] Calculated trade amount: {trade_amount} {symbol} at price ${current_price}")
            return trade_amount

        except Exception as e:
            logger.error(f"❌ [REBALANCER] Error calculating trade amount: {e}")
            return Decimal('0')

    async def _execute_single_step_trade(self, client, from_currency: str, to_currency: str, amount: Decimal, exchange_name: str) -> Dict[str, Any]:
        """Execute a single step of a multi-step trade"""
        try:
            logger.info(f"🔄 [REBALANCER] Single step: {from_currency} -> {to_currency}, amount: ${amount}")

            # Create a temporary action for this step
            step_action = RebalancingAction(
                currency_from=from_currency,
                currency_to=to_currency,
                amount=amount,
                percentage=Decimal('0'),
                reason="Multi-step rebalancing",
                priority=1,
                expected_impact=Decimal('0'),
                risk_adjustment=1.0
            )

            # Determine trading parameters
            symbol, side = await self._determine_trading_parameters(step_action, exchange_name)
            if not symbol:
                return {"success": False, "error": f"No valid trading pair for {from_currency} -> {to_currency}"}

            # Calculate trade amount
            trade_amount = await self._calculate_trade_amount(step_action, symbol, side, exchange_name)
            if trade_amount <= 0:
                return {"success": False, "error": "Invalid trade amount calculated"}

            # Execute the trade
            if hasattr(client, 'place_order'):
                result = await client.place_order(
                    symbol=symbol,
                    side=side,
                    amount=float(trade_amount),
                    order_type='market'
                )

                if 'error' not in result:
                    logger.info(f"✅ [REBALANCER] Step completed: {symbol} {side} {trade_amount}")
                    return {
                        "success": True,
                        "symbol": symbol,
                        "side": side,
                        "amount": trade_amount,
                        "result": result,
                        "received_amount": amount * Decimal('0.95')  # Estimate with 5% slippage
                    }
                else:
                    return {"success": False, "error": result.get('error', 'Trade failed')}
            else:
                return {"success": False, "error": "Client does not support place_order"}

        except Exception as e:
            logger.error(f"❌ [REBALANCER] Error in single step trade: {e}")
            return {"success": False, "error": str(e)}



    async def run_continuous_rebalancing(self):
        """Run continuous portfolio rebalancing"""
        try:
            logger.info("🚀 [REBALANCER] Starting continuous portfolio rebalancing...")

            while True:
                try:
                    # Identify rebalancing needs
                    actions = await self.identify_rebalancing_needs()

                    if actions:
                        # Execute the highest priority action
                        best_action = actions[0]
                        result = await self.execute_rebalancing_action(best_action)

                        if result.get('success', False):
                            logger.info("✅ [REBALANCER] Successfully executed rebalancing action")
                        else:
                            logger.warning(f"⚠️ [REBALANCER] Failed to execute rebalancing: {result.get('error')}")
                    else:
                        logger.info("✅ [REBALANCER] Portfolio is well-balanced, no action needed")

                    # Wait before next rebalancing check
                    await asyncio.sleep(300)  # 5 minute intervals

                except Exception as e:
                    logger.error(f"❌ [REBALANCER] Error in rebalancing loop: {e}")
                    await asyncio.sleep(600)  # Wait longer on error

        except Exception as e:
            logger.error(f"❌ [REBALANCER] Critical error in continuous rebalancing: {e}")
            raise
