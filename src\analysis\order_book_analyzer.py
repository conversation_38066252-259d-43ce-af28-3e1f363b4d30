"""
Advanced Order Book Analysis for Market Microstructure
Provides deep insights into order book dynamics and liquidity patterns
"""

import asyncio
import logging
import time
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
from dataclasses import dataclass
from collections import defaultdict, deque
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

@dataclass
class OrderBookLevel:
    """Represents a single order book level"""
    price: float
    size: float
    orders: int = 1
    timestamp: float = 0.0

@dataclass
class OrderBookSnapshot:
    """Complete order book snapshot"""
    symbol: str
    timestamp: float
    bids: List[OrderBookLevel]
    asks: List[OrderBookLevel]
    spread: float
    mid_price: float
    total_bid_volume: float
    total_ask_volume: float

@dataclass
class LiquidityMetrics:
    """Comprehensive liquidity metrics"""
    symbol: str
    timestamp: float
    bid_depth: float
    ask_depth: float
    total_depth: float
    spread_bps: float
    price_impact_1pct: float
    price_impact_5pct: float
    order_book_imbalance: float
    liquidity_score: float

class AdvancedOrderBookAnalyzer:
    """Advanced order book analyzer for market microstructure insights"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        
        # Historical data storage
        self.order_book_history = defaultdict(lambda: deque(maxlen=max_history))
        self.liquidity_history = defaultdict(lambda: deque(maxlen=max_history))
        self.spread_history = defaultdict(lambda: deque(maxlen=max_history))
        
        # Analysis caches
        self.imbalance_cache = defaultdict(list)
        self.volatility_cache = defaultdict(list)
        self.depth_profile_cache = defaultdict(dict)
        
        # Performance tracking
        self.analysis_times = []
        
        logger.info("📊 [ORDER-BOOK] Advanced order book analyzer initialized")

    async def analyze_order_book(self, symbol: str, order_book: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive order book analysis"""
        try:
            start_time = time.time()
            
            # Parse order book data
            snapshot = self._parse_order_book(symbol, order_book)
            
            # Calculate liquidity metrics
            liquidity_metrics = await self._calculate_liquidity_metrics(snapshot)
            
            # Analyze order book imbalance
            imbalance_analysis = await self._analyze_order_book_imbalance(snapshot)
            
            # Calculate price impact
            price_impact = await self._calculate_price_impact(snapshot)
            
            # Analyze depth profile
            depth_profile = await self._analyze_depth_profile(snapshot)
            
            # Detect order book patterns
            patterns = await self._detect_order_book_patterns(symbol, snapshot)
            
            # Calculate execution quality metrics
            execution_metrics = await self._calculate_execution_metrics(snapshot)
            
            # Store historical data
            self._store_historical_data(symbol, snapshot, liquidity_metrics)
            
            analysis_time = (time.time() - start_time) * 1000
            self.analysis_times.append(analysis_time)
            
            return {
                'snapshot': snapshot,
                'liquidity_metrics': liquidity_metrics,
                'imbalance_analysis': imbalance_analysis,
                'price_impact': price_impact,
                'depth_profile': depth_profile,
                'patterns': patterns,
                'execution_metrics': execution_metrics,
                'analysis_time_ms': analysis_time,
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error analyzing order book for {symbol}: {e}")
            return self._get_default_analysis(symbol)

    def _parse_order_book(self, symbol: str, order_book: Dict[str, Any]) -> OrderBookSnapshot:
        """Parse raw order book data into structured format"""
        try:
            bids_raw = order_book.get('bids', [])
            asks_raw = order_book.get('asks', [])
            
            # Parse bids (highest price first)
            bids = []
            for bid in bids_raw[:20]:  # Top 20 levels
                if len(bid) >= 2:
                    price = float(bid[0])
                    size = float(bid[1])
                    orders = int(bid[2]) if len(bid) > 2 else 1
                    bids.append(OrderBookLevel(price, size, orders, time.time()))
            
            # Parse asks (lowest price first)
            asks = []
            for ask in asks_raw[:20]:  # Top 20 levels
                if len(ask) >= 2:
                    price = float(ask[0])
                    size = float(ask[1])
                    orders = int(ask[2]) if len(ask) > 2 else 1
                    asks.append(OrderBookLevel(price, size, orders, time.time()))
            
            # Calculate basic metrics
            best_bid = bids[0].price if bids else 0.0
            best_ask = asks[0].price if asks else 0.0
            spread = best_ask - best_bid if best_bid > 0 and best_ask > 0 else 0.0
            mid_price = (best_bid + best_ask) / 2 if best_bid > 0 and best_ask > 0 else 0.0
            
            total_bid_volume = sum(bid.size for bid in bids)
            total_ask_volume = sum(ask.size for ask in asks)
            
            return OrderBookSnapshot(
                symbol=symbol,
                timestamp=time.time(),
                bids=bids,
                asks=asks,
                spread=spread,
                mid_price=mid_price,
                total_bid_volume=total_bid_volume,
                total_ask_volume=total_ask_volume
            )
            
        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error parsing order book: {e}")
            return OrderBookSnapshot(
                symbol=symbol,
                timestamp=time.time(),
                bids=[],
                asks=[],
                spread=0.0,
                mid_price=0.0,
                total_bid_volume=0.0,
                total_ask_volume=0.0
            )

    async def _calculate_liquidity_metrics(self, snapshot: OrderBookSnapshot) -> LiquidityMetrics:
        """Calculate comprehensive liquidity metrics"""
        try:
            # Basic depth metrics
            bid_depth = sum(bid.size for bid in snapshot.bids[:10])  # Top 10 levels
            ask_depth = sum(ask.size for ask in snapshot.asks[:10])
            total_depth = bid_depth + ask_depth
            
            # Spread in basis points
            spread_bps = (snapshot.spread / snapshot.mid_price * 10000) if snapshot.mid_price > 0 else 0
            
            # Price impact calculations
            price_impact_1pct = await self._calculate_price_impact_for_percentage(snapshot, 0.01)
            price_impact_5pct = await self._calculate_price_impact_for_percentage(snapshot, 0.05)
            
            # Order book imbalance
            imbalance = (bid_depth - ask_depth) / (bid_depth + ask_depth) if total_depth > 0 else 0
            
            # Liquidity score (0-1, higher is better)
            liquidity_score = self._calculate_liquidity_score(
                total_depth, spread_bps, price_impact_1pct, abs(imbalance)
            )
            
            return LiquidityMetrics(
                symbol=snapshot.symbol,
                timestamp=snapshot.timestamp,
                bid_depth=bid_depth,
                ask_depth=ask_depth,
                total_depth=total_depth,
                spread_bps=spread_bps,
                price_impact_1pct=price_impact_1pct,
                price_impact_5pct=price_impact_5pct,
                order_book_imbalance=imbalance,
                liquidity_score=liquidity_score
            )
            
        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error calculating liquidity metrics: {e}")
            return LiquidityMetrics(
                symbol=snapshot.symbol,
                timestamp=snapshot.timestamp,
                bid_depth=0.0,
                ask_depth=0.0,
                total_depth=0.0,
                spread_bps=100.0,
                price_impact_1pct=0.01,
                price_impact_5pct=0.05,
                order_book_imbalance=0.0,
                liquidity_score=0.5
            )

    async def _calculate_price_impact_for_percentage(self, snapshot: OrderBookSnapshot, 
                                                   percentage: float) -> float:
        """Calculate price impact for a given percentage of daily volume"""
        try:
            # Estimate daily volume (simplified)
            estimated_daily_volume = snapshot.total_bid_volume * 100  # Rough estimate
            target_volume = estimated_daily_volume * percentage
            
            # Calculate impact on ask side (buy order)
            cumulative_volume = 0.0
            weighted_price = 0.0
            
            for ask in snapshot.asks:
                if cumulative_volume >= target_volume:
                    break
                
                volume_to_consume = min(ask.size, target_volume - cumulative_volume)
                weighted_price += ask.price * volume_to_consume
                cumulative_volume += volume_to_consume
            
            if cumulative_volume > 0:
                avg_execution_price = weighted_price / cumulative_volume
                impact = (avg_execution_price - snapshot.mid_price) / snapshot.mid_price
                return abs(impact)
            
            return 0.01  # Default 1% impact
            
        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error calculating price impact: {e}")
            return 0.01

    def _calculate_liquidity_score(self, total_depth: float, spread_bps: float,
                                 price_impact: float, imbalance: float) -> float:
        """Calculate overall liquidity score"""
        try:
            # Normalize factors (0-1, higher is better)
            depth_score = min(1.0, total_depth / 1000)  # Normalize to 1000 units
            spread_score = max(0.0, 1.0 - spread_bps / 100)  # 100 bps = 0 score
            impact_score = max(0.0, 1.0 - price_impact * 100)  # 1% impact = 0 score
            balance_score = max(0.0, 1.0 - abs(imbalance))  # Perfect balance = 1
            
            # Weighted combination
            liquidity_score = (
                depth_score * 0.4 +
                spread_score * 0.3 +
                impact_score * 0.2 +
                balance_score * 0.1
            )
            
            return max(0.0, min(1.0, liquidity_score))
            
        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error calculating liquidity score: {e}")
            return 0.5

    async def _analyze_order_book_imbalance(self, snapshot: OrderBookSnapshot) -> Dict[str, Any]:
        """Analyze order book imbalance patterns"""
        try:
            # Calculate imbalance at different depths
            imbalances = {}
            
            for depth in [1, 5, 10, 20]:
                bid_volume = sum(bid.size for bid in snapshot.bids[:depth])
                ask_volume = sum(ask.size for ask in snapshot.asks[:depth])
                total_volume = bid_volume + ask_volume
                
                if total_volume > 0:
                    imbalance = (bid_volume - ask_volume) / total_volume
                    imbalances[f'depth_{depth}'] = imbalance
                else:
                    imbalances[f'depth_{depth}'] = 0.0
            
            # Overall imbalance trend
            avg_imbalance = np.mean(list(imbalances.values()))
            imbalance_strength = abs(avg_imbalance)
            
            # Classify imbalance
            if imbalance_strength > 0.3:
                imbalance_level = 'high'
            elif imbalance_strength > 0.1:
                imbalance_level = 'medium'
            else:
                imbalance_level = 'low'
            
            # Direction
            direction = 'bullish' if avg_imbalance > 0 else 'bearish' if avg_imbalance < 0 else 'neutral'
            
            return {
                'imbalances_by_depth': imbalances,
                'average_imbalance': avg_imbalance,
                'imbalance_strength': imbalance_strength,
                'imbalance_level': imbalance_level,
                'direction': direction,
                'confidence': min(1.0, imbalance_strength * 2)
            }
            
        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error analyzing imbalance: {e}")
            return {'average_imbalance': 0.0, 'imbalance_level': 'low', 'direction': 'neutral'}

    async def _calculate_price_impact(self, snapshot: OrderBookSnapshot) -> Dict[str, Any]:
        """Calculate detailed price impact analysis"""
        try:
            # Calculate impact for different order sizes
            impact_analysis = {}
            
            # Test different order sizes (as percentage of book depth)
            test_sizes = [0.1, 0.25, 0.5, 1.0, 2.0, 5.0]  # Percentages
            
            for size_pct in test_sizes:
                # Calculate for both buy and sell
                buy_impact = self._calculate_impact_for_size(snapshot, size_pct, 'buy')
                sell_impact = self._calculate_impact_for_size(snapshot, size_pct, 'sell')
                
                impact_analysis[f'size_{size_pct}pct'] = {
                    'buy_impact': buy_impact,
                    'sell_impact': sell_impact,
                    'average_impact': (buy_impact + sell_impact) / 2
                }
            
            # Market impact model parameters
            impact_model = self._fit_impact_model(impact_analysis)
            
            return {
                'impact_by_size': impact_analysis,
                'impact_model': impact_model,
                'liquidity_cost': impact_analysis.get('size_1.0pct', {}).get('average_impact', 0.01)
            }
            
        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error calculating price impact: {e}")
            return {'liquidity_cost': 0.01}

    def _calculate_impact_for_size(self, snapshot: OrderBookSnapshot, 
                                 size_percentage: float, side: str) -> float:
        """Calculate price impact for specific order size and side"""
        try:
            if side == 'buy':
                levels = snapshot.asks
                reference_price = snapshot.asks[0].price if snapshot.asks else snapshot.mid_price
            else:
                levels = snapshot.bids
                reference_price = snapshot.bids[0].price if snapshot.bids else snapshot.mid_price
            
            # Calculate target volume
            total_volume = sum(level.size for level in levels[:10])
            target_volume = total_volume * (size_percentage / 100)
            
            # Walk through order book
            cumulative_volume = 0.0
            weighted_price = 0.0
            
            for level in levels:
                if cumulative_volume >= target_volume:
                    break
                
                volume_to_consume = min(level.size, target_volume - cumulative_volume)
                weighted_price += level.price * volume_to_consume
                cumulative_volume += volume_to_consume
            
            if cumulative_volume > 0:
                avg_price = weighted_price / cumulative_volume
                impact = abs(avg_price - reference_price) / reference_price
                return impact
            
            return 0.0
            
        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error calculating impact for size: {e}")
            return 0.01

    def _fit_impact_model(self, impact_analysis: Dict) -> Dict[str, float]:
        """Fit a simple market impact model"""
        try:
            # Extract size and impact data
            sizes = []
            impacts = []
            
            for key, data in impact_analysis.items():
                if key.startswith('size_'):
                    size = float(key.replace('size_', '').replace('pct', ''))
                    impact = data.get('average_impact', 0)
                    sizes.append(size)
                    impacts.append(impact)
            
            if len(sizes) >= 2:
                # Simple linear fit: impact = alpha * size^beta
                # For simplicity, assume beta = 0.5 (square root law)
                alpha = np.mean([impact / (size ** 0.5) for size, impact in zip(sizes, impacts) if size > 0])
                
                return {
                    'alpha': alpha,
                    'beta': 0.5,
                    'model_type': 'power_law',
                    'r_squared': 0.8  # Simplified
                }
            
            return {'alpha': 0.01, 'beta': 0.5, 'model_type': 'default'}
            
        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error fitting impact model: {e}")
            return {'alpha': 0.01, 'beta': 0.5, 'model_type': 'default'}

    def _get_default_analysis(self, symbol: str) -> Dict[str, Any]:
        """Return default analysis when errors occur"""
        return {
            'snapshot': None,
            'liquidity_metrics': None,
            'imbalance_analysis': {'average_imbalance': 0.0, 'direction': 'neutral'},
            'price_impact': {'liquidity_cost': 0.01},
            'depth_profile': {},
            'patterns': [],
            'execution_metrics': {},
            'analysis_time_ms': 0.0,
            'timestamp': time.time(),
            'error': 'Analysis failed'
        }

    async def _analyze_depth_profile(self, snapshot: OrderBookSnapshot) -> Dict[str, Any]:
        """Analyze order book depth profile"""
        try:
            # Calculate cumulative depth at different price levels
            bid_profile = []
            ask_profile = []

            # Analyze bid side
            cumulative_bid_volume = 0.0
            for i, bid in enumerate(snapshot.bids):
                cumulative_bid_volume += bid.size
                price_distance = (snapshot.mid_price - bid.price) / snapshot.mid_price if snapshot.mid_price > 0 else 0
                bid_profile.append({
                    'level': i + 1,
                    'price': bid.price,
                    'size': bid.size,
                    'cumulative_size': cumulative_bid_volume,
                    'price_distance_pct': price_distance * 100
                })

            # Analyze ask side
            cumulative_ask_volume = 0.0
            for i, ask in enumerate(snapshot.asks):
                cumulative_ask_volume += ask.size
                price_distance = (ask.price - snapshot.mid_price) / snapshot.mid_price if snapshot.mid_price > 0 else 0
                ask_profile.append({
                    'level': i + 1,
                    'price': ask.price,
                    'size': ask.size,
                    'cumulative_size': cumulative_ask_volume,
                    'price_distance_pct': price_distance * 100
                })

            # Calculate depth concentration
            total_bid_volume = cumulative_bid_volume
            total_ask_volume = cumulative_ask_volume

            # Top 5 levels concentration
            top5_bid_volume = sum(bid.size for bid in snapshot.bids[:5])
            top5_ask_volume = sum(ask.size for ask in snapshot.asks[:5])

            bid_concentration = top5_bid_volume / total_bid_volume if total_bid_volume > 0 else 0
            ask_concentration = top5_ask_volume / total_ask_volume if total_ask_volume > 0 else 0

            return {
                'bid_profile': bid_profile,
                'ask_profile': ask_profile,
                'bid_concentration_top5': bid_concentration,
                'ask_concentration_top5': ask_concentration,
                'depth_symmetry': abs(total_bid_volume - total_ask_volume) / (total_bid_volume + total_ask_volume) if (total_bid_volume + total_ask_volume) > 0 else 0
            }

        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error analyzing depth profile: {e}")
            return {}

    async def _detect_order_book_patterns(self, symbol: str, snapshot: OrderBookSnapshot) -> List[Dict[str, Any]]:
        """Detect patterns in order book structure"""
        try:
            patterns = []

            # Pattern 1: Large orders (icebergs)
            large_orders = self._detect_large_orders(snapshot)
            if large_orders:
                patterns.extend(large_orders)

            # Pattern 2: Price clustering
            clustering = self._detect_price_clustering(snapshot)
            if clustering:
                patterns.append(clustering)

            # Pattern 3: Spread anomalies
            spread_anomaly = self._detect_spread_anomalies(symbol, snapshot)
            if spread_anomaly:
                patterns.append(spread_anomaly)

            # Pattern 4: Depth imbalances
            depth_imbalance = self._detect_depth_imbalances(snapshot)
            if depth_imbalance:
                patterns.append(depth_imbalance)

            return patterns

        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error detecting patterns: {e}")
            return []

    def _detect_large_orders(self, snapshot: OrderBookSnapshot) -> List[Dict[str, Any]]:
        """Detect unusually large orders that might be icebergs"""
        try:
            patterns = []

            # Calculate average order size
            all_sizes = [bid.size for bid in snapshot.bids] + [ask.size for ask in snapshot.asks]
            if not all_sizes:
                return patterns

            avg_size = np.mean(all_sizes)
            threshold = avg_size * 5  # 5x average size

            # Check bids
            for i, bid in enumerate(snapshot.bids):
                if bid.size > threshold:
                    patterns.append({
                        'type': 'large_bid',
                        'level': i + 1,
                        'price': bid.price,
                        'size': bid.size,
                        'size_ratio': bid.size / avg_size,
                        'confidence': min(1.0, bid.size / (threshold * 2))
                    })

            # Check asks
            for i, ask in enumerate(snapshot.asks):
                if ask.size > threshold:
                    patterns.append({
                        'type': 'large_ask',
                        'level': i + 1,
                        'price': ask.price,
                        'size': ask.size,
                        'size_ratio': ask.size / avg_size,
                        'confidence': min(1.0, ask.size / (threshold * 2))
                    })

            return patterns

        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error detecting large orders: {e}")
            return []

    def _detect_price_clustering(self, snapshot: OrderBookSnapshot) -> Optional[Dict[str, Any]]:
        """Detect price clustering patterns"""
        try:
            # Analyze price level distribution
            bid_prices = [bid.price for bid in snapshot.bids[:10]]
            ask_prices = [ask.price for ask in snapshot.asks[:10]]

            # Check for round number clustering
            round_levels = [0.01, 0.05, 0.1, 0.5, 1.0, 5.0, 10.0]

            clustering_score = 0.0
            for price in bid_prices + ask_prices:
                for level in round_levels:
                    if abs(price % level) < level * 0.1:  # Within 10% of round number
                        clustering_score += 1

            total_levels = len(bid_prices) + len(ask_prices)
            clustering_ratio = clustering_score / total_levels if total_levels > 0 else 0

            if clustering_ratio > 0.3:  # More than 30% clustering
                return {
                    'type': 'price_clustering',
                    'clustering_ratio': clustering_ratio,
                    'confidence': min(1.0, clustering_ratio * 2),
                    'description': f'{clustering_ratio:.1%} of prices show clustering'
                }

            return None

        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error detecting price clustering: {e}")
            return None

    def _detect_spread_anomalies(self, symbol: str, snapshot: OrderBookSnapshot) -> Optional[Dict[str, Any]]:
        """Detect spread anomalies"""
        try:
            current_spread = snapshot.spread

            # Get historical spread data
            historical_spreads = list(self.spread_history[symbol])

            if len(historical_spreads) < 10:
                return None

            # Calculate spread statistics
            avg_spread = np.mean(historical_spreads)
            std_spread = np.std(historical_spreads)

            # Detect anomalies
            z_score = abs(current_spread - avg_spread) / std_spread if std_spread > 0 else 0

            if z_score > 2.0:  # More than 2 standard deviations
                anomaly_type = 'wide_spread' if current_spread > avg_spread else 'tight_spread'

                return {
                    'type': 'spread_anomaly',
                    'anomaly_type': anomaly_type,
                    'current_spread': current_spread,
                    'average_spread': avg_spread,
                    'z_score': z_score,
                    'confidence': min(1.0, z_score / 3.0)
                }

            return None

        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error detecting spread anomalies: {e}")
            return None

    def _detect_depth_imbalances(self, snapshot: OrderBookSnapshot) -> Optional[Dict[str, Any]]:
        """Detect significant depth imbalances"""
        try:
            bid_depth = sum(bid.size for bid in snapshot.bids[:5])
            ask_depth = sum(ask.size for ask in snapshot.asks[:5])
            total_depth = bid_depth + ask_depth

            if total_depth == 0:
                return None

            imbalance = abs(bid_depth - ask_depth) / total_depth

            if imbalance > 0.3:  # More than 30% imbalance
                dominant_side = 'bid' if bid_depth > ask_depth else 'ask'

                return {
                    'type': 'depth_imbalance',
                    'imbalance_ratio': imbalance,
                    'dominant_side': dominant_side,
                    'bid_depth': bid_depth,
                    'ask_depth': ask_depth,
                    'confidence': min(1.0, imbalance * 2)
                }

            return None

        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error detecting depth imbalances: {e}")
            return None

    async def _calculate_execution_metrics(self, snapshot: OrderBookSnapshot) -> Dict[str, Any]:
        """Calculate execution quality metrics"""
        try:
            # Effective spread
            effective_spread = snapshot.spread / snapshot.mid_price if snapshot.mid_price > 0 else 0

            # Realized spread (simplified)
            realized_spread = effective_spread * 0.7  # Assume 70% of effective spread

            # Price improvement opportunity
            if len(snapshot.bids) > 1 and len(snapshot.asks) > 1:
                bid_improvement = (snapshot.bids[1].price - snapshot.bids[0].price) / snapshot.mid_price
                ask_improvement = (snapshot.asks[0].price - snapshot.asks[1].price) / snapshot.mid_price
                price_improvement = (abs(bid_improvement) + abs(ask_improvement)) / 2
            else:
                price_improvement = 0.0

            # Execution probability (based on depth)
            total_depth = snapshot.total_bid_volume + snapshot.total_ask_volume
            execution_probability = min(1.0, total_depth / 1000)  # Normalize to 1000 units

            return {
                'effective_spread': effective_spread,
                'realized_spread': realized_spread,
                'price_improvement_opportunity': price_improvement,
                'execution_probability': execution_probability,
                'market_quality_score': (1 - effective_spread) * execution_probability
            }

        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error calculating execution metrics: {e}")
            return {}

    def _store_historical_data(self, symbol: str, snapshot: OrderBookSnapshot,
                             liquidity_metrics: LiquidityMetrics):
        """Store historical data for trend analysis"""
        try:
            self.order_book_history[symbol].append(snapshot)
            self.liquidity_history[symbol].append(liquidity_metrics)
            self.spread_history[symbol].append(snapshot.spread)

        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error storing historical data: {e}")

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary of the analyzer"""
        try:
            if not self.analysis_times:
                return {'average_analysis_time_ms': 0, 'total_analyses': 0}

            return {
                'average_analysis_time_ms': np.mean(self.analysis_times),
                'max_analysis_time_ms': max(self.analysis_times),
                'min_analysis_time_ms': min(self.analysis_times),
                'total_analyses': len(self.analysis_times),
                'symbols_tracked': len(self.order_book_history)
            }

        except Exception as e:
            logger.error(f"❌ [ORDER-BOOK] Error getting performance summary: {e}")
            return {'error': str(e)}

# Export the main class
__all__ = ['AdvancedOrderBookAnalyzer', 'OrderBookSnapshot', 'LiquidityMetrics', 'OrderBookLevel']
