#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to comment out the specific orphaned section from line 3719 to 3793
"""

def comment_orphaned_section():
    """Comment out the orphaned section"""
    
    with open('main.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Comment out lines 3719 to 3793 (0-based indexing: 3718 to 3792)
    start_line = 3718  # 0-based
    end_line = 3792    # 0-based
    
    for i in range(start_line, min(end_line + 1, len(lines))):
        line = lines[i]
        if not line.strip().startswith('#') and line.strip():
            # Comment out the line
            lines[i] = '        # ORPHANED: ' + line.lstrip()
        elif line.strip():
            # Already a comment, just add prefix
            lines[i] = '        # ORPHANED: ' + line.lstrip()
    
    # Write the fixed file
    with open('main_commented.py', 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print(f"Commented out orphaned section from line 3719 to 3793")
    print(f"Output saved to main_commented.py")

if __name__ == "__main__":
    comment_orphaned_section()
