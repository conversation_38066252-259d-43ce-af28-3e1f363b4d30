#!/usr/bin/env python3
"""
<PERSON>ript to fix all method indentations in the file
"""

import re

def fix_all_methods():
    """Fix all method indentations"""
    
    with open('main_commented.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    fixed_lines = []
    
    for i, line in enumerate(lines):
        # Check if this is a method definition that's incorrectly indented
        if re.match(r'\s*(async\s+)?def\s+', line):
            # Extract the method definition
            method_match = re.match(r'\s*(async\s+def\s+.*|def\s+.*)', line)
            if method_match:
                method_def = method_match.group(1)
                # Set proper indentation (4 spaces for class methods)
                fixed_lines.append('    ' + method_def + '\n')
                continue
        
        # Keep the line as is
        fixed_lines.append(line)
    
    # Write the fixed file
    with open('main_methods_fixed.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print(f"Fixed all method indentations")
    print(f"Output saved to main_methods_fixed.py")

if __name__ == "__main__":
    fix_all_methods()
