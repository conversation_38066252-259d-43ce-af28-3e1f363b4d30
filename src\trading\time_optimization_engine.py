"""
Time-Optimization Engine for All Trading Strategies
===================================================

Dedicated time-optimization components, real-time profit-per-unit-time metrics,
Bayesian optimization for time-weighted hyperparameter tuning, meta-learning
system for knowledge transfer acceleration, global time-efficiency optimizer,
and comprehensive time-series analysis of profit velocity.

THE GOLDEN RULE: MAXIMUM PROFIT IN MINIMUM TIME

Features:
- Real-time profit-per-unit-time metrics for all strategies
- Bayesian optimization for time-weighted hyperparameter tuning
- Meta-learning system that accelerates knowledge transfer between strategies
- Global time-efficiency optimizer that coordinates all trading engines
- Comprehensive time-series analysis of profit velocity patterns
- Dynamic strategy allocation based on time-efficiency scores
- Cross-strategy performance correlation analysis
- Automated strategy switching for maximum time efficiency
"""

import asyncio
import logging
import time
import numpy as np
import pandas as pd
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Any, Set, Union
from dataclasses import dataclass, field
from enum import Enum
import json
import math
from collections import defaultdict, deque
from scipy.optimize import minimize
from scipy.stats import norm
import warnings
warnings.filterwarnings('ignore')

# Import neural network components
try:
    from ..neural.enhanced_profit_predictor import EnhancedProfitPredictor
    from ..neural.reinforcement_learning import ReinforcementLearningAgent
    from ..neural.lstm_processor import LSTMProcessor
    from ..neural.transformer_trading_model import TransformerTradingModel
    from ..neural.temporal_intelligence import AdvancedTemporalIntelligence
    NEURAL_COMPONENTS_AVAILABLE = True
except ImportError:
    NEURAL_COMPONENTS_AVAILABLE = False

# Import performance optimization
try:
    from ..performance.speed_optimizer import fast_api_call, cached_market_data
    PERFORMANCE_OPTIMIZATIONS_AVAILABLE = True
except ImportError:
    PERFORMANCE_OPTIMIZATIONS_AVAILABLE = False

logger = logging.getLogger(__name__)

class StrategyType(Enum):
    """Trading strategy types"""
    FUTURES_BASIS = "futures_basis"
    GRID_TRADING = "grid_trading"
    MARKET_MAKING = "market_making"
    VOLATILITY_OPTIONS = "volatility_options"
    YIELD_OPTIMIZATION = "yield_optimization"

class TimeMetric(Enum):
    """Time-based performance metrics"""
    PROFIT_PER_MINUTE = "profit_per_minute"
    TIME_TO_PROFIT = "time_to_profit"
    PROFIT_VELOCITY = "profit_velocity"
    TIME_EFFICIENCY = "time_efficiency"
    VELOCITY_ACCELERATION = "velocity_acceleration"

class OptimizationObjective(Enum):
    """Optimization objectives"""
    MAXIMIZE_PROFIT_VELOCITY = "maximize_profit_velocity"
    MINIMIZE_TIME_TO_PROFIT = "minimize_time_to_profit"
    MAXIMIZE_TIME_EFFICIENCY = "maximize_time_efficiency"
    OPTIMIZE_RISK_ADJUSTED_VELOCITY = "optimize_risk_adjusted_velocity"

@dataclass
class TimePerformanceMetrics:
    """Time-based performance metrics for strategies"""
    strategy_type: StrategyType
    profit_per_minute: float
    time_to_profit: float  # Average time to first profit (minutes)
    profit_velocity: float  # Rate of profit acceleration
    time_efficiency_score: float  # Overall time efficiency (0-1)
    velocity_consistency: float  # Consistency of profit velocity
    risk_adjusted_velocity: float  # Velocity adjusted for risk
    opportunity_frequency: float  # Opportunities per hour
    execution_speed: float  # Average execution time (seconds)
    learning_velocity: float  # Rate of strategy improvement
    timestamp: float = field(default_factory=time.time)

@dataclass
class StrategyAllocation:
    """Strategy allocation based on time efficiency"""
    strategy_type: StrategyType
    allocation_percentage: float
    expected_profit_velocity: float
    confidence: float
    time_horizon: float  # Expected time to target profit (minutes)
    risk_score: float
    last_updated: float = field(default_factory=time.time)

@dataclass
class TimeOptimizationState:
    """Current state of time optimization"""
    total_profit_velocity: float
    best_performing_strategy: StrategyType
    worst_performing_strategy: StrategyType
    velocity_trend: float  # Positive = accelerating, negative = decelerating
    efficiency_score: float  # Overall system efficiency (0-1)
    rebalance_urgency: float  # How urgently rebalancing is needed (0-1)
    learning_progress: float  # Rate of system learning (0-1)
    next_optimization_time: float

class RealTimeProfitTracker:
    """Real-time profit-per-unit-time metrics tracker"""
    
    def __init__(self):
        self.strategy_metrics = defaultdict(lambda: defaultdict(deque))
        self.global_metrics = defaultdict(deque)
        self.profit_history = defaultdict(deque)
        self.time_windows = [60, 300, 900, 3600]  # 1min, 5min, 15min, 1hour
        
    async def record_profit_event(self, strategy_type: StrategyType, profit: float, 
                                execution_time: float, metadata: Dict = None):
        """Record a profit event for real-time tracking"""
        try:
            current_time = time.time()
            
            # Record profit event
            profit_event = {
                'profit': profit,
                'execution_time': execution_time,
                'timestamp': current_time,
                'metadata': metadata or {}
            }
            
            # Store in strategy-specific history
            self.profit_history[strategy_type].append(profit_event)
            
            # Calculate real-time metrics for different time windows
            for window_seconds in self.time_windows:
                window_metrics = await self._calculate_window_metrics(
                    strategy_type, window_seconds, current_time
                )
                
                self.strategy_metrics[strategy_type][window_seconds].append(window_metrics)
                
                # Keep only recent metrics
                if len(self.strategy_metrics[strategy_type][window_seconds]) > 1000:
                    self.strategy_metrics[strategy_type][window_seconds].popleft()
            
            # Update global metrics
            await self._update_global_metrics(current_time)
            
            # Clean old data
            await self._cleanup_old_data(current_time)
            
        except Exception as e:
            logger.error(f"Error recording profit event: {e}")
    
    async def _calculate_window_metrics(self, strategy_type: StrategyType, 
                                      window_seconds: int, current_time: float) -> Dict:
        """Calculate metrics for a specific time window"""
        try:
            cutoff_time = current_time - window_seconds
            
            # Get events in window
            window_events = [
                event for event in self.profit_history[strategy_type]
                if event['timestamp'] >= cutoff_time
            ]
            
            if not window_events:
                return {
                    'profit_per_minute': 0.0,
                    'total_profit': 0.0,
                    'event_count': 0,
                    'avg_execution_time': 0.0,
                    'profit_velocity': 0.0,
                    'timestamp': current_time
                }
            
            # Calculate metrics
            total_profit = sum(event['profit'] for event in window_events)
            window_minutes = window_seconds / 60
            profit_per_minute = total_profit / window_minutes
            
            execution_times = [event['execution_time'] for event in window_events]
            avg_execution_time = np.mean(execution_times)
            
            # Calculate profit velocity (acceleration)
            if len(window_events) >= 2:
                profits = [event['profit'] for event in window_events]
                timestamps = [event['timestamp'] for event in window_events]
                
                # Linear regression for velocity
                time_diffs = np.array(timestamps) - timestamps[0]
                if len(time_diffs) > 1 and np.std(time_diffs) > 0:
                    velocity = np.polyfit(time_diffs, profits, 1)[0] * 60  # Per minute
                else:
                    velocity = 0.0
            else:
                velocity = 0.0
            
            return {
                'profit_per_minute': profit_per_minute,
                'total_profit': total_profit,
                'event_count': len(window_events),
                'avg_execution_time': avg_execution_time,
                'profit_velocity': velocity,
                'timestamp': current_time
            }
            
        except Exception as e:
            logger.error(f"Error calculating window metrics: {e}")
            return {}
    
    async def _update_global_metrics(self, current_time: float):
        """Update global system metrics"""
        try:
            # Calculate cross-strategy metrics
            total_profit_per_minute = 0.0
            total_events = 0
            
            for strategy_type in StrategyType:
                if strategy_type in self.strategy_metrics:
                    # Get latest 5-minute metrics
                    recent_metrics = self.strategy_metrics[strategy_type][300]
                    if recent_metrics:
                        latest = recent_metrics[-1]
                        total_profit_per_minute += latest.get('profit_per_minute', 0)
                        total_events += latest.get('event_count', 0)
            
            # Store global metrics
            global_metric = {
                'total_profit_per_minute': total_profit_per_minute,
                'total_events': total_events,
                'active_strategies': len([s for s in StrategyType if s in self.strategy_metrics]),
                'timestamp': current_time
            }
            
            self.global_metrics['system'].append(global_metric)
            
            # Keep only recent global metrics
            if len(self.global_metrics['system']) > 1000:
                self.global_metrics['system'].popleft()
                
        except Exception as e:
            logger.error(f"Error updating global metrics: {e}")
    
    async def _cleanup_old_data(self, current_time: float):
        """Clean up old data to prevent memory issues"""
        try:
            # Keep only last 24 hours of profit history
            cutoff_time = current_time - (24 * 3600)
            
            for strategy_type in list(self.profit_history.keys()):
                # Filter recent events
                recent_events = [
                    event for event in self.profit_history[strategy_type]
                    if event['timestamp'] >= cutoff_time
                ]
                
                # Replace with recent events
                self.profit_history[strategy_type] = deque(recent_events, maxlen=10000)
                
        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")
    
    async def get_current_metrics(self, strategy_type: StrategyType = None, 
                                window_minutes: int = 5) -> Dict:
        """Get current performance metrics"""
        try:
            window_seconds = window_minutes * 60
            
            if strategy_type:
                # Get metrics for specific strategy
                if (strategy_type in self.strategy_metrics and 
                    window_seconds in self.strategy_metrics[strategy_type]):
                    
                    recent_metrics = self.strategy_metrics[strategy_type][window_seconds]
                    if recent_metrics:
                        return recent_metrics[-1]
                
                return {}
            else:
                # Get global metrics
                if self.global_metrics['system']:
                    return self.global_metrics['system'][-1]
                
                return {}
                
        except Exception as e:
            logger.error(f"Error getting current metrics: {e}")
            return {}

class BayesianTimeOptimizer:
    """Bayesian optimization for time-weighted hyperparameter tuning"""
    
    def __init__(self):
        self.optimization_history = defaultdict(list)
        self.parameter_bounds = {}
        self.acquisition_function = "expected_improvement"
        self.exploration_weight = 0.1
        
    async def optimize_strategy_parameters(self, strategy_type: StrategyType,
                                         current_params: Dict, 
                                         performance_history: List[Dict]) -> Dict:
        """Optimize strategy parameters using Bayesian optimization"""
        try:
            if len(performance_history) < 5:
                return current_params  # Need minimum data for optimization
            
            # Define parameter bounds for the strategy
            bounds = self._get_parameter_bounds(strategy_type)
            
            if not bounds:
                return current_params
            
            # Prepare optimization data
            X, y = self._prepare_optimization_data(performance_history, bounds.keys())
            
            if len(X) < 3:
                return current_params
            
            # Perform Bayesian optimization
            optimal_params = await self._bayesian_optimize(X, y, bounds, current_params)
            
            # Validate and return optimized parameters
            validated_params = self._validate_parameters(optimal_params, bounds)
            
            logger.info(f"🔧 [BAYESIAN-OPT] Optimized parameters for {strategy_type.value}")
            
            return validated_params
            
        except Exception as e:
            logger.error(f"Error in Bayesian optimization: {e}")
            return current_params
    
    def _get_parameter_bounds(self, strategy_type: StrategyType) -> Dict:
        """Get parameter bounds for different strategies"""
        try:
            bounds = {
                StrategyType.FUTURES_BASIS: {
                    'leverage_multiplier': (0.5, 2.0),
                    'confidence_threshold': (0.6, 0.95),
                    'time_decay_factor': (0.9, 0.99),
                    'profit_target_multiplier': (1.5, 3.0)
                },
                StrategyType.GRID_TRADING: {
                    'grid_spacing_multiplier': (0.5, 2.0),
                    'grid_count_factor': (0.7, 1.5),
                    'profit_margin_multiplier': (0.8, 1.5),
                    'rebalance_threshold': (0.03, 0.1)
                },
                StrategyType.MARKET_MAKING: {
                    'spread_multiplier': (0.7, 1.5),
                    'inventory_target_factor': (0.5, 1.5),
                    'quote_refresh_multiplier': (0.5, 2.0),
                    'risk_adjustment_factor': (0.8, 1.3)
                },
                StrategyType.VOLATILITY_OPTIONS: {
                    'volatility_threshold': (0.15, 0.4),
                    'time_decay_preference': (0.7, 1.3),
                    'gamma_target_multiplier': (0.5, 2.0),
                    'confidence_threshold': (0.6, 0.9)
                },
                StrategyType.YIELD_OPTIMIZATION: {
                    'time_weight_factor': (0.8, 2.0),
                    'risk_tolerance': (0.5, 1.5),
                    'compound_preference': (0.7, 1.3),
                    'liquidity_weight': (0.6, 1.4)
                }
            }
            
            return bounds.get(strategy_type, {})
            
        except Exception as e:
            logger.error(f"Error getting parameter bounds: {e}")
            return {}
    
    def _prepare_optimization_data(self, performance_history: List[Dict], 
                                 param_names: List[str]) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare data for optimization"""
        try:
            X = []
            y = []
            
            for record in performance_history:
                if 'parameters' in record and 'profit_velocity' in record:
                    # Extract parameter values
                    param_values = []
                    for param_name in param_names:
                        value = record['parameters'].get(param_name, 1.0)
                        param_values.append(value)
                    
                    if len(param_values) == len(param_names):
                        X.append(param_values)
                        y.append(record['profit_velocity'])
            
            return np.array(X), np.array(y)
            
        except Exception as e:
            logger.error(f"Error preparing optimization data: {e}")
            return np.array([]), np.array([])
    
    async def _bayesian_optimize(self, X: np.ndarray, y: np.ndarray, 
                               bounds: Dict, current_params: Dict) -> Dict:
        """Perform Bayesian optimization"""
        try:
            # Simplified Bayesian optimization using scipy
            param_names = list(bounds.keys())
            
            def objective(params):
                # Create parameter dict
                param_dict = dict(zip(param_names, params))
                
                # Calculate expected improvement (simplified)
                # In practice, would use Gaussian Process
                distances = []
                for i in range(len(X)):
                    dist = np.linalg.norm(X[i] - params)
                    distances.append(dist)
                
                if distances:
                    # Weight by inverse distance
                    weights = 1.0 / (np.array(distances) + 1e-6)
                    weights = weights / np.sum(weights)
                    expected_value = np.sum(weights * y)
                    
                    # Add exploration bonus
                    min_distance = np.min(distances)
                    exploration_bonus = self.exploration_weight * min_distance
                    
                    return -(expected_value + exploration_bonus)  # Minimize negative
                else:
                    return 0.0
            
            # Set up bounds for scipy
            scipy_bounds = [(bounds[name][0], bounds[name][1]) for name in param_names]
            
            # Initial guess from current parameters
            x0 = [current_params.get(name, 1.0) for name in param_names]
            
            # Optimize
            result = minimize(objective, x0, bounds=scipy_bounds, method='L-BFGS-B')
            
            if result.success:
                # Convert back to parameter dict
                optimal_params = dict(zip(param_names, result.x))
                return optimal_params
            else:
                return current_params
                
        except Exception as e:
            logger.error(f"Error in Bayesian optimization: {e}")
            return current_params
    
    def _validate_parameters(self, params: Dict, bounds: Dict) -> Dict:
        """Validate and constrain parameters within bounds"""
        try:
            validated = {}
            
            for param_name, value in params.items():
                if param_name in bounds:
                    min_val, max_val = bounds[param_name]
                    validated[param_name] = max(min_val, min(max_val, value))
                else:
                    validated[param_name] = value
            
            return validated
            
        except Exception as e:
            logger.error(f"Error validating parameters: {e}")
            return params

class MetaLearningSystem:
    """Meta-learning system for knowledge transfer acceleration"""

    def __init__(self):
        self.strategy_knowledge = defaultdict(dict)
        self.cross_strategy_patterns = defaultdict(list)
        self.transfer_success_rates = defaultdict(float)
        self.learning_velocity_tracker = defaultdict(deque)

        # Initialize neural components if available
        if NEURAL_COMPONENTS_AVAILABLE:
            # Initialize transformer with proper config
            from src.neural.transformer_trading_model import TransformerConfig
            transformer_config = TransformerConfig(
                d_model=128,
                num_heads=4,
                num_layers=3,
                d_ff=256,
                dropout=0.1
            )
            self.transformer_model = TransformerTradingModel(transformer_config, input_size=20, output_size=1)
            self.temporal_intelligence = AdvancedTemporalIntelligence()

    async def extract_strategy_knowledge(self, strategy_type: StrategyType,
                                       performance_data: List[Dict]) -> Dict:
        """Extract transferable knowledge from strategy performance"""
        try:
            if len(performance_data) < 10:
                return {}

            # Extract patterns and insights
            knowledge = {
                'optimal_conditions': await self._identify_optimal_conditions(performance_data),
                'failure_patterns': await self._identify_failure_patterns(performance_data),
                'time_patterns': await self._extract_time_patterns(performance_data),
                'parameter_sensitivities': await self._analyze_parameter_sensitivity(performance_data),
                'market_regime_preferences': await self._analyze_market_regimes(performance_data),
                'velocity_drivers': await self._identify_velocity_drivers(performance_data)
            }

            # Store knowledge
            self.strategy_knowledge[strategy_type] = knowledge

            # Update cross-strategy patterns
            await self._update_cross_strategy_patterns(strategy_type, knowledge)

            logger.info(f"🧠 [META-LEARNING] Extracted knowledge for {strategy_type.value}")

            return knowledge

        except Exception as e:
            logger.error(f"Error extracting strategy knowledge: {e}")
            return {}

    async def _identify_optimal_conditions(self, performance_data: List[Dict]) -> Dict:
        """Identify conditions that lead to optimal performance"""
        try:
            # Sort by profit velocity
            sorted_data = sorted(performance_data, key=lambda x: x.get('profit_velocity', 0), reverse=True)

            # Take top 20% performers
            top_performers = sorted_data[:max(1, len(sorted_data) // 5)]

            # Analyze common conditions
            conditions = {
                'avg_volatility': np.mean([d.get('volatility', 0) for d in top_performers]),
                'avg_volume': np.mean([d.get('volume', 0) for d in top_performers]),
                'preferred_time_of_day': self._find_preferred_time(top_performers),
                'optimal_market_conditions': self._analyze_market_conditions(top_performers),
                'success_rate': len(top_performers) / len(performance_data)
            }

            return conditions

        except Exception as e:
            logger.error(f"Error identifying optimal conditions: {e}")
            return {}

    async def _identify_failure_patterns(self, performance_data: List[Dict]) -> Dict:
        """Identify patterns that lead to poor performance"""
        try:
            # Sort by profit velocity (ascending for failures)
            sorted_data = sorted(performance_data, key=lambda x: x.get('profit_velocity', 0))

            # Take bottom 20% performers
            poor_performers = sorted_data[:max(1, len(sorted_data) // 5)]

            # Analyze failure patterns
            patterns = {
                'common_volatility_range': self._analyze_volatility_failures(poor_performers),
                'problematic_times': self._find_problematic_times(poor_performers),
                'parameter_extremes': self._find_parameter_extremes(poor_performers),
                'market_condition_failures': self._analyze_failure_conditions(poor_performers)
            }

            return patterns

        except Exception as e:
            logger.error(f"Error identifying failure patterns: {e}")
            return {}

    async def _extract_time_patterns(self, performance_data: List[Dict]) -> Dict:
        """Extract time-based performance patterns"""
        try:
            # Group by time periods
            hourly_performance = defaultdict(list)
            daily_performance = defaultdict(list)

            for data in performance_data:
                timestamp = data.get('timestamp', time.time())
                dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)

                hour = dt.hour
                day_of_week = dt.weekday()

                profit_velocity = data.get('profit_velocity', 0)

                hourly_performance[hour].append(profit_velocity)
                daily_performance[day_of_week].append(profit_velocity)

            # Calculate averages
            hourly_avg = {hour: np.mean(velocities) for hour, velocities in hourly_performance.items()}
            daily_avg = {day: np.mean(velocities) for day, velocities in daily_performance.items()}

            # Find best and worst times
            best_hour = max(hourly_avg.items(), key=lambda x: x[1])[0] if hourly_avg else 0
            worst_hour = min(hourly_avg.items(), key=lambda x: x[1])[0] if hourly_avg else 0

            best_day = max(daily_avg.items(), key=lambda x: x[1])[0] if daily_avg else 0
            worst_day = min(daily_avg.items(), key=lambda x: x[1])[0] if daily_avg else 0

            return {
                'hourly_performance': hourly_avg,
                'daily_performance': daily_avg,
                'best_hour': best_hour,
                'worst_hour': worst_hour,
                'best_day': best_day,
                'worst_day': worst_day,
                'time_sensitivity': np.std(list(hourly_avg.values())) if hourly_avg else 0
            }

        except Exception as e:
            logger.error(f"Error extracting time patterns: {e}")
            return {}

    async def _analyze_parameter_sensitivity(self, performance_data: List[Dict]) -> Dict:
        """Analyze sensitivity to parameter changes"""
        try:
            sensitivities = {}

            # Group data by parameter values
            parameter_groups = defaultdict(lambda: defaultdict(list))

            for data in performance_data:
                parameters = data.get('parameters', {})
                profit_velocity = data.get('profit_velocity', 0)

                for param_name, param_value in parameters.items():
                    # Discretize parameter values for grouping
                    discretized_value = round(param_value, 2)
                    parameter_groups[param_name][discretized_value].append(profit_velocity)

            # Calculate sensitivity for each parameter
            for param_name, value_groups in parameter_groups.items():
                if len(value_groups) > 1:
                    values = list(value_groups.keys())
                    avg_performances = [np.mean(velocities) for velocities in value_groups.values()]

                    # Calculate sensitivity as standard deviation of performance
                    sensitivity = np.std(avg_performances)
                    sensitivities[param_name] = sensitivity

            return sensitivities

        except Exception as e:
            logger.error(f"Error analyzing parameter sensitivity: {e}")
            return {}

    async def _analyze_market_regimes(self, performance_data: List[Dict]) -> Dict:
        """Analyze performance in different market regimes"""
        try:
            regime_performance = defaultdict(list)

            for data in performance_data:
                # Classify market regime based on volatility and trend
                volatility = data.get('volatility', 0.02)
                trend = data.get('trend', 0.0)
                profit_velocity = data.get('profit_velocity', 0)

                if volatility > 0.03:
                    regime = 'high_volatility'
                elif volatility < 0.01:
                    regime = 'low_volatility'
                else:
                    regime = 'normal_volatility'

                if abs(trend) > 0.02:
                    regime += '_trending'
                else:
                    regime += '_ranging'

                regime_performance[regime].append(profit_velocity)

            # Calculate average performance per regime
            regime_averages = {
                regime: np.mean(velocities)
                for regime, velocities in regime_performance.items()
            }

            # Find best and worst regimes
            best_regime = max(regime_averages.items(), key=lambda x: x[1])[0] if regime_averages else 'unknown'
            worst_regime = min(regime_averages.items(), key=lambda x: x[1])[0] if regime_averages else 'unknown'

            return {
                'regime_performance': regime_averages,
                'best_regime': best_regime,
                'worst_regime': worst_regime,
                'regime_adaptability': np.std(list(regime_averages.values())) if regime_averages else 0
            }

        except Exception as e:
            logger.error(f"Error analyzing market regimes: {e}")
            return {}

    async def _identify_velocity_drivers(self, performance_data: List[Dict]) -> Dict:
        """Identify key drivers of profit velocity"""
        try:
            # Correlation analysis between various factors and profit velocity
            factors = ['volatility', 'volume', 'spread', 'liquidity', 'confidence']
            correlations = {}

            for factor in factors:
                factor_values = []
                velocities = []

                for data in performance_data:
                    if factor in data and 'profit_velocity' in data:
                        factor_values.append(data[factor])
                        velocities.append(data['profit_velocity'])

                if len(factor_values) > 5:
                    correlation = np.corrcoef(factor_values, velocities)[0, 1]
                    correlations[factor] = correlation if not np.isnan(correlation) else 0.0

            # Rank factors by correlation strength
            ranked_drivers = sorted(correlations.items(), key=lambda x: abs(x[1]), reverse=True)

            return {
                'correlations': correlations,
                'top_drivers': ranked_drivers[:3],
                'velocity_predictability': np.mean([abs(corr) for corr in correlations.values()])
            }

        except Exception as e:
            logger.error(f"Error identifying velocity drivers: {e}")
            return {}

    async def _update_cross_strategy_patterns(self, strategy_type: StrategyType, knowledge: Dict):
        """Update cross-strategy patterns for knowledge transfer"""
        try:
            # Extract transferable patterns
            transferable_patterns = {
                'optimal_volatility_range': knowledge.get('optimal_conditions', {}).get('avg_volatility', 0),
                'preferred_times': knowledge.get('time_patterns', {}).get('best_hour', 0),
                'parameter_sensitivities': knowledge.get('parameter_sensitivities', {}),
                'velocity_drivers': knowledge.get('velocity_drivers', {})
            }

            # Store for cross-strategy analysis
            self.cross_strategy_patterns[strategy_type].append(transferable_patterns)

            # Keep only recent patterns
            if len(self.cross_strategy_patterns[strategy_type]) > 100:
                self.cross_strategy_patterns[strategy_type] = self.cross_strategy_patterns[strategy_type][-100:]

        except Exception as e:
            logger.error(f"Error updating cross-strategy patterns: {e}")

    async def transfer_knowledge(self, from_strategy: StrategyType,
                               to_strategy: StrategyType) -> Dict:
        """Transfer knowledge between strategies"""
        try:
            if from_strategy not in self.strategy_knowledge:
                return {}

            source_knowledge = self.strategy_knowledge[from_strategy]

            # Identify transferable insights
            transferable_insights = {
                'optimal_times': source_knowledge.get('time_patterns', {}),
                'market_preferences': source_knowledge.get('market_regime_preferences', {}),
                'velocity_optimization': source_knowledge.get('velocity_drivers', {}),
                'risk_patterns': source_knowledge.get('failure_patterns', {})
            }

            # Calculate transfer confidence based on strategy similarity
            transfer_confidence = await self._calculate_transfer_confidence(from_strategy, to_strategy)

            # Apply transfer confidence weighting
            weighted_insights = {}
            for key, value in transferable_insights.items():
                if isinstance(value, dict):
                    weighted_insights[key] = {
                        k: v * transfer_confidence for k, v in value.items()
                        if isinstance(v, (int, float))
                    }
                else:
                    weighted_insights[key] = value

            # Update transfer success tracking
            self.transfer_success_rates[f"{from_strategy.value}_to_{to_strategy.value}"] = transfer_confidence

            logger.info(f"🔄 [KNOWLEDGE-TRANSFER] Transferred knowledge from {from_strategy.value} "
                       f"to {to_strategy.value} (confidence: {transfer_confidence:.2f})")

            return weighted_insights

        except Exception as e:
            logger.error(f"Error transferring knowledge: {e}")
            return {}

    async def _calculate_transfer_confidence(self, from_strategy: StrategyType,
                                           to_strategy: StrategyType) -> float:
        """Calculate confidence in knowledge transfer between strategies"""
        try:
            # Strategy similarity matrix (based on characteristics)
            similarity_matrix = {
                (StrategyType.FUTURES_BASIS, StrategyType.VOLATILITY_OPTIONS): 0.7,
                (StrategyType.GRID_TRADING, StrategyType.MARKET_MAKING): 0.8,
                (StrategyType.MARKET_MAKING, StrategyType.GRID_TRADING): 0.8,
                (StrategyType.VOLATILITY_OPTIONS, StrategyType.FUTURES_BASIS): 0.7,
                (StrategyType.YIELD_OPTIMIZATION, StrategyType.FUTURES_BASIS): 0.5,
                (StrategyType.YIELD_OPTIMIZATION, StrategyType.GRID_TRADING): 0.4,
                (StrategyType.YIELD_OPTIMIZATION, StrategyType.MARKET_MAKING): 0.4,
                (StrategyType.YIELD_OPTIMIZATION, StrategyType.VOLATILITY_OPTIONS): 0.3,
            }

            # Get base similarity
            base_similarity = similarity_matrix.get((from_strategy, to_strategy), 0.3)

            # Adjust based on historical transfer success
            transfer_key = f"{from_strategy.value}_to_{to_strategy.value}"
            historical_success = self.transfer_success_rates.get(transfer_key, 0.5)

            # Combine base similarity with historical success
            confidence = (base_similarity + historical_success) / 2

            return min(0.95, max(0.1, confidence))

        except Exception as e:
            logger.error(f"Error calculating transfer confidence: {e}")
            return 0.5

    def _find_preferred_time(self, performance_data: List[Dict]) -> int:
        """Find preferred time of day from performance data"""
        try:
            hour_counts = defaultdict(int)

            for data in performance_data:
                timestamp = data.get('timestamp', time.time())
                hour = datetime.fromtimestamp(timestamp, tz=timezone.utc).hour
                hour_counts[hour] += 1

            if hour_counts:
                return max(hour_counts.items(), key=lambda x: x[1])[0]

            return 12  # Default to noon UTC

        except Exception as e:
            logger.error(f"Error finding preferred time: {e}")
            return 12

    def _analyze_market_conditions(self, performance_data: List[Dict]) -> Dict:
        """Analyze optimal market conditions"""
        try:
            conditions = {
                'volatility_range': [
                    np.percentile([d.get('volatility', 0.02) for d in performance_data], 25),
                    np.percentile([d.get('volatility', 0.02) for d in performance_data], 75)
                ],
                'volume_range': [
                    np.percentile([d.get('volume', 1000) for d in performance_data], 25),
                    np.percentile([d.get('volume', 1000) for d in performance_data], 75)
                ]
            }

            return conditions

        except Exception as e:
            logger.error(f"Error analyzing market conditions: {e}")
            return {}

    def _analyze_volatility_failures(self, poor_performers: List[Dict]) -> Tuple[float, float]:
        """Analyze volatility ranges that lead to failures"""
        try:
            volatilities = [d.get('volatility', 0.02) for d in poor_performers]
            if volatilities:
                return (min(volatilities), max(volatilities))
            return (0.0, 0.0)

        except Exception as e:
            logger.error(f"Error analyzing volatility failures: {e}")
            return (0.0, 0.0)

    def _find_problematic_times(self, poor_performers: List[Dict]) -> List[int]:
        """Find times that are problematic for performance"""
        try:
            problem_hours = defaultdict(int)

            for data in poor_performers:
                timestamp = data.get('timestamp', time.time())
                hour = datetime.fromtimestamp(timestamp, tz=timezone.utc).hour
                problem_hours[hour] += 1

            # Return hours with high failure rates
            total_failures = len(poor_performers)
            threshold = total_failures * 0.1  # 10% threshold

            problematic = [hour for hour, count in problem_hours.items() if count > threshold]

            return problematic

        except Exception as e:
            logger.error(f"Error finding problematic times: {e}")
            return []

    def _find_parameter_extremes(self, poor_performers: List[Dict]) -> Dict:
        """Find parameter values that lead to poor performance"""
        try:
            extreme_params = defaultdict(list)

            for data in poor_performers:
                parameters = data.get('parameters', {})
                for param_name, param_value in parameters.items():
                    extreme_params[param_name].append(param_value)

            # Calculate ranges for each parameter
            param_ranges = {}
            for param_name, values in extreme_params.items():
                if values:
                    param_ranges[param_name] = {
                        'min': min(values),
                        'max': max(values),
                        'avg': np.mean(values)
                    }

            return param_ranges

        except Exception as e:
            logger.error(f"Error finding parameter extremes: {e}")
            return {}

    def _analyze_failure_conditions(self, poor_performers: List[Dict]) -> Dict:
        """Analyze market conditions during failures"""
        try:
            failure_conditions = {
                'avg_volatility': np.mean([d.get('volatility', 0.02) for d in poor_performers]),
                'avg_volume': np.mean([d.get('volume', 1000) for d in poor_performers]),
                'common_trends': self._analyze_trend_failures(poor_performers)
            }

            return failure_conditions

        except Exception as e:
            logger.error(f"Error analyzing failure conditions: {e}")
            return {}

    def _analyze_trend_failures(self, poor_performers: List[Dict]) -> str:
        """Analyze trend conditions during failures"""
        try:
            trends = [d.get('trend', 0.0) for d in poor_performers]
            avg_trend = np.mean(trends)

            if avg_trend > 0.01:
                return 'strong_uptrend'
            elif avg_trend < -0.01:
                return 'strong_downtrend'
            else:
                return 'sideways'

        except Exception as e:
            logger.error(f"Error analyzing trend failures: {e}")
            return 'unknown'

class GlobalTimeEfficiencyOptimizer:
    """
    Global time-efficiency optimizer that coordinates all trading strategies
    for maximum profit in minimum time
    """

    def __init__(self, exchange_clients: Dict, config: Dict = None):
        self.exchange_clients = exchange_clients
        self.config = config or {}

        # Initialize components
        self.profit_tracker = RealTimeProfitTracker()
        self.bayesian_optimizer = BayesianTimeOptimizer()
        self.meta_learning = MetaLearningSystem()

        # Initialize neural components if available
        if NEURAL_COMPONENTS_AVAILABLE:
            self.profit_predictor = EnhancedProfitPredictor()
            self.rl_agent = ReinforcementLearningAgent()

        # Strategy engines (will be injected)
        self.strategy_engines = {}

        # Optimization state
        self.current_allocations = {}
        self.optimization_state = TimeOptimizationState(
            total_profit_velocity=0.0,
            best_performing_strategy=StrategyType.FUTURES_BASIS,
            worst_performing_strategy=StrategyType.YIELD_OPTIMIZATION,
            velocity_trend=0.0,
            efficiency_score=0.0,
            rebalance_urgency=0.0,
            learning_progress=0.0,
            next_optimization_time=time.time() + 300
        )

        # Performance tracking
        self.strategy_performance = defaultdict(lambda: defaultdict(deque))
        self.global_performance_history = deque(maxlen=1000)

        # Configuration
        self.optimization_interval = self.config.get('optimization_interval', 300)  # 5 minutes
        self.rebalance_threshold = self.config.get('rebalance_threshold', 0.2)  # 20% performance difference
        self.min_allocation_percentage = self.config.get('min_allocation_percentage', 0.05)  # 5% minimum
        self.max_allocation_percentage = self.config.get('max_allocation_percentage', 0.6)   # 60% maximum

        logger.info("⚡ [TIME-OPTIMIZER] Global Time-Efficiency Optimizer initialized")

    def register_strategy_engine(self, strategy_type: StrategyType, engine):
        """Register a strategy engine for optimization"""
        self.strategy_engines[strategy_type] = engine
        logger.info(f"📝 [REGISTER] Registered {strategy_type.value} engine")

    async def start_optimization(self):
        """Start the global time optimization loop"""
        logger.info("🎯 [TIME-OPTIMIZER] Starting global time-efficiency optimization...")

        try:
            while True:
                start_time = time.time()

                # 1. Collect performance metrics from all strategies
                await self._collect_strategy_metrics()

                # 2. Update optimization state
                await self._update_optimization_state()

                # 3. Perform Bayesian optimization on strategy parameters
                await self._optimize_strategy_parameters()

                # 4. Update meta-learning knowledge
                await self._update_meta_learning()

                # 5. Calculate optimal strategy allocations
                optimal_allocations = await self._calculate_optimal_allocations()

                # 6. Rebalance if needed
                await self._rebalance_strategies(optimal_allocations)

                # 7. Update learning systems
                await self._update_learning_systems()

                # 8. Log performance metrics
                await self._log_optimization_metrics()

                # Calculate loop time and sleep
                loop_time = time.time() - start_time
                target_loop_time = self.optimization_interval

                if loop_time < target_loop_time:
                    await asyncio.sleep(target_loop_time - loop_time)

                logger.debug(f"⚡ [TIME-OPTIMIZER] Optimization loop completed in {loop_time:.2f}s")

        except Exception as e:
            logger.error(f"❌ [TIME-OPTIMIZER] Optimization engine error: {e}")
            raise

    async def _collect_strategy_metrics(self):
        """Collect performance metrics from all strategy engines"""
        try:
            for strategy_type, engine in self.strategy_engines.items():
                try:
                    # Get current metrics from profit tracker
                    current_metrics = await self.profit_tracker.get_current_metrics(strategy_type)

                    if current_metrics:
                        # Create performance metrics object
                        performance = TimePerformanceMetrics(
                            strategy_type=strategy_type,
                            profit_per_minute=current_metrics.get('profit_per_minute', 0),
                            time_to_profit=current_metrics.get('avg_execution_time', 0) / 60,  # Convert to minutes
                            profit_velocity=current_metrics.get('profit_velocity', 0),
                            time_efficiency_score=self._calculate_time_efficiency_score(current_metrics),
                            velocity_consistency=self._calculate_velocity_consistency(strategy_type),
                            risk_adjusted_velocity=self._calculate_risk_adjusted_velocity(current_metrics),
                            opportunity_frequency=current_metrics.get('event_count', 0) / 5,  # Per 5-minute window
                            execution_speed=current_metrics.get('avg_execution_time', 0),
                            learning_velocity=self._calculate_learning_velocity(strategy_type)
                        )

                        # Store performance metrics
                        self.strategy_performance[strategy_type]['current'] = performance
                        self.strategy_performance[strategy_type]['history'].append(performance)

                        # Keep only recent history
                        if len(self.strategy_performance[strategy_type]['history']) > 100:
                            self.strategy_performance[strategy_type]['history'].popleft()

                except Exception as e:
                    logger.debug(f"Error collecting metrics for {strategy_type.value}: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error collecting strategy metrics: {e}")

    def _calculate_time_efficiency_score(self, metrics: Dict) -> float:
        """Calculate overall time efficiency score"""
        try:
            profit_per_minute = metrics.get('profit_per_minute', 0)
            execution_time = metrics.get('avg_execution_time', 1)
            event_count = metrics.get('event_count', 0)

            # Normalize components
            profit_score = min(1.0, profit_per_minute * 1000)  # Scale profit
            speed_score = max(0.1, 1.0 / (execution_time + 1))  # Inverse of execution time
            frequency_score = min(1.0, event_count / 10)  # Normalize event frequency

            # Weighted combination
            efficiency_score = (profit_score * 0.5 + speed_score * 0.3 + frequency_score * 0.2)

            return max(0.0, min(1.0, efficiency_score))

        except Exception as e:
            logger.error(f"Error calculating time efficiency score: {e}")
            return 0.0

    def _calculate_velocity_consistency(self, strategy_type: StrategyType) -> float:
        """Calculate consistency of profit velocity"""
        try:
            history = self.strategy_performance[strategy_type]['history']

            if len(history) < 5:
                return 0.5  # Default consistency

            # Get recent velocity values
            velocities = [perf.profit_velocity for perf in list(history)[-10:]]

            if not velocities or all(v == 0 for v in velocities):
                return 0.0

            # Calculate coefficient of variation (lower = more consistent)
            mean_velocity = np.mean(velocities)
            std_velocity = np.std(velocities)

            if mean_velocity == 0:
                return 0.0

            cv = std_velocity / abs(mean_velocity)
            consistency = max(0.0, 1.0 - cv)  # Invert so higher = more consistent

            return min(1.0, consistency)

        except Exception as e:
            logger.error(f"Error calculating velocity consistency: {e}")
            return 0.5

    def _calculate_risk_adjusted_velocity(self, metrics: Dict) -> float:
        """Calculate risk-adjusted profit velocity"""
        try:
            profit_velocity = metrics.get('profit_velocity', 0)

            # Simple risk adjustment (would be more sophisticated in practice)
            # Assume higher frequency = lower risk per trade
            event_count = metrics.get('event_count', 1)
            risk_factor = min(1.0, event_count / 5)  # More events = lower risk

            risk_adjusted = profit_velocity * risk_factor

            return risk_adjusted

        except Exception as e:
            logger.error(f"Error calculating risk-adjusted velocity: {e}")
            return 0.0

    def _calculate_learning_velocity(self, strategy_type: StrategyType) -> float:
        """Calculate how quickly the strategy is improving"""
        try:
            history = self.strategy_performance[strategy_type]['history']

            if len(history) < 10:
                return 0.5  # Default learning velocity

            # Get recent performance trend
            recent_scores = [perf.time_efficiency_score for perf in list(history)[-10:]]

            if len(recent_scores) < 2:
                return 0.5

            # Calculate trend (linear regression slope)
            x = np.arange(len(recent_scores))
            trend = np.polyfit(x, recent_scores, 1)[0]

            # Normalize to 0-1 range
            learning_velocity = max(0.0, min(1.0, trend * 10 + 0.5))

            return learning_velocity

        except Exception as e:
            logger.error(f"Error calculating learning velocity: {e}")
            return 0.5

    async def _update_optimization_state(self):
        """Update the global optimization state"""
        try:
            # Calculate total profit velocity
            total_velocity = 0.0
            strategy_velocities = {}

            for strategy_type in StrategyType:
                if strategy_type in self.strategy_performance:
                    current_perf = self.strategy_performance[strategy_type].get('current')
                    if current_perf:
                        velocity = current_perf.profit_velocity
                        strategy_velocities[strategy_type] = velocity
                        total_velocity += velocity

            # Find best and worst performing strategies
            if strategy_velocities:
                best_strategy = max(strategy_velocities.items(), key=lambda x: x[1])[0]
                worst_strategy = min(strategy_velocities.items(), key=lambda x: x[1])[0]
            else:
                best_strategy = StrategyType.FUTURES_BASIS
                worst_strategy = StrategyType.YIELD_OPTIMIZATION

            # Calculate velocity trend
            velocity_trend = self._calculate_velocity_trend()

            # Calculate overall efficiency score
            efficiency_score = self._calculate_overall_efficiency()

            # Calculate rebalance urgency
            rebalance_urgency = self._calculate_rebalance_urgency(strategy_velocities)

            # Calculate learning progress
            learning_progress = self._calculate_learning_progress()

            # Update optimization state
            self.optimization_state.total_profit_velocity = total_velocity
            self.optimization_state.best_performing_strategy = best_strategy
            self.optimization_state.worst_performing_strategy = worst_strategy
            self.optimization_state.velocity_trend = velocity_trend
            self.optimization_state.efficiency_score = efficiency_score
            self.optimization_state.rebalance_urgency = rebalance_urgency
            self.optimization_state.learning_progress = learning_progress

            # Store global performance
            global_perf = {
                'total_velocity': total_velocity,
                'efficiency_score': efficiency_score,
                'active_strategies': len(strategy_velocities),
                'timestamp': time.time()
            }

            self.global_performance_history.append(global_perf)

        except Exception as e:
            logger.error(f"Error updating optimization state: {e}")

    def _calculate_velocity_trend(self) -> float:
        """Calculate the trend in profit velocity"""
        try:
            if len(self.global_performance_history) < 5:
                return 0.0

            # Get recent velocity values
            recent_velocities = [perf['total_velocity'] for perf in list(self.global_performance_history)[-10:]]

            if len(recent_velocities) < 2:
                return 0.0

            # Calculate trend
            x = np.arange(len(recent_velocities))
            trend = np.polyfit(x, recent_velocities, 1)[0]

            return trend

        except Exception as e:
            logger.error(f"Error calculating velocity trend: {e}")
            return 0.0

    def _calculate_overall_efficiency(self) -> float:
        """Calculate overall system efficiency"""
        try:
            total_efficiency = 0.0
            strategy_count = 0

            for strategy_type in StrategyType:
                if strategy_type in self.strategy_performance:
                    current_perf = self.strategy_performance[strategy_type].get('current')
                    if current_perf:
                        total_efficiency += current_perf.time_efficiency_score
                        strategy_count += 1

            if strategy_count > 0:
                return total_efficiency / strategy_count

            return 0.0

        except Exception as e:
            logger.error(f"Error calculating overall efficiency: {e}")
            return 0.0

    def _calculate_rebalance_urgency(self, strategy_velocities: Dict) -> float:
        """Calculate how urgently rebalancing is needed"""
        try:
            if len(strategy_velocities) < 2:
                return 0.0

            velocities = list(strategy_velocities.values())
            max_velocity = max(velocities)
            min_velocity = min(velocities)

            if max_velocity == 0:
                return 0.0

            # Calculate performance spread
            velocity_spread = (max_velocity - min_velocity) / max_velocity

            # Urgency increases with spread
            urgency = min(1.0, velocity_spread / self.rebalance_threshold)

            return urgency

        except Exception as e:
            logger.error(f"Error calculating rebalance urgency: {e}")
            return 0.0

    def _calculate_learning_progress(self) -> float:
        """Calculate overall learning progress"""
        try:
            total_learning = 0.0
            strategy_count = 0

            for strategy_type in StrategyType:
                if strategy_type in self.strategy_performance:
                    current_perf = self.strategy_performance[strategy_type].get('current')
                    if current_perf:
                        total_learning += current_perf.learning_velocity
                        strategy_count += 1

            if strategy_count > 0:
                return total_learning / strategy_count

            return 0.0

        except Exception as e:
            logger.error(f"Error calculating learning progress: {e}")
            return 0.0

    async def _optimize_strategy_parameters(self):
        """Optimize parameters for all strategies using Bayesian optimization"""
        try:
            for strategy_type, engine in self.strategy_engines.items():
                try:
                    # Get performance history for the strategy
                    history = list(self.strategy_performance[strategy_type]['history'])

                    if len(history) < 5:
                        continue  # Need minimum data

                    # Convert to format expected by Bayesian optimizer
                    performance_data = []
                    for perf in history:
                        data = {
                            'profit_velocity': perf.profit_velocity,
                            'timestamp': perf.timestamp,
                            'parameters': getattr(engine, 'current_parameters', {}),
                            'volatility': 0.02,  # Would get from market data
                            'volume': 1000,     # Would get from market data
                            'trend': 0.0        # Would get from market data
                        }
                        performance_data.append(data)

                    # Get current parameters
                    current_params = getattr(engine, 'current_parameters', {})

                    # Optimize parameters
                    optimal_params = await self.bayesian_optimizer.optimize_strategy_parameters(
                        strategy_type, current_params, performance_data
                    )

                    # Apply optimized parameters to engine
                    if hasattr(engine, 'update_parameters'):
                        await engine.update_parameters(optimal_params)
                        logger.info(f"🔧 [OPTIMIZE] Updated parameters for {strategy_type.value}")

                except Exception as e:
                    logger.debug(f"Error optimizing parameters for {strategy_type.value}: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error optimizing strategy parameters: {e}")

    async def _update_meta_learning(self):
        """Update meta-learning knowledge for all strategies"""
        try:
            for strategy_type in StrategyType:
                if strategy_type in self.strategy_performance:
                    # Get performance history
                    history = list(self.strategy_performance[strategy_type]['history'])

                    if len(history) >= 10:
                        # Convert to format for meta-learning
                        performance_data = []
                        for perf in history:
                            data = {
                                'profit_velocity': perf.profit_velocity,
                                'timestamp': perf.timestamp,
                                'volatility': 0.02,  # Would get from market data
                                'volume': 1000,     # Would get from market data
                                'trend': 0.0,       # Would get from market data
                                'parameters': {}    # Would get from engine
                            }
                            performance_data.append(data)

                        # Extract knowledge
                        await self.meta_learning.extract_strategy_knowledge(strategy_type, performance_data)

            # Perform knowledge transfer between similar strategies
            await self._perform_knowledge_transfer()

        except Exception as e:
            logger.error(f"Error updating meta-learning: {e}")

    async def _perform_knowledge_transfer(self):
        """Perform knowledge transfer between strategies"""
        try:
            # Define transfer pairs (from high-performing to low-performing)
            transfer_pairs = [
                (self.optimization_state.best_performing_strategy, self.optimization_state.worst_performing_strategy)
            ]

            for from_strategy, to_strategy in transfer_pairs:
                if from_strategy != to_strategy:
                    transferred_knowledge = await self.meta_learning.transfer_knowledge(
                        from_strategy, to_strategy
                    )

                    if transferred_knowledge:
                        # Apply transferred knowledge to target strategy
                        target_engine = self.strategy_engines.get(to_strategy)
                        if target_engine and hasattr(target_engine, 'apply_transferred_knowledge'):
                            await target_engine.apply_transferred_knowledge(transferred_knowledge)

        except Exception as e:
            logger.error(f"Error performing knowledge transfer: {e}")

    async def _calculate_optimal_allocations(self) -> Dict[StrategyType, float]:
        """Calculate optimal capital allocation across strategies"""
        try:
            # Get current performance metrics
            strategy_scores = {}

            for strategy_type in StrategyType:
                if strategy_type in self.strategy_performance:
                    current_perf = self.strategy_performance[strategy_type].get('current')
                    if current_perf:
                        # Calculate composite score
                        score = (
                            current_perf.profit_velocity * 0.4 +
                            current_perf.time_efficiency_score * 0.3 +
                            current_perf.velocity_consistency * 0.2 +
                            current_perf.learning_velocity * 0.1
                        )
                        strategy_scores[strategy_type] = max(0.01, score)  # Minimum score

            if not strategy_scores:
                # Default equal allocation
                equal_allocation = 1.0 / len(StrategyType)
                return {strategy: equal_allocation for strategy in StrategyType}

            # Calculate allocations based on scores
            total_score = sum(strategy_scores.values())
            allocations = {}

            for strategy_type, score in strategy_scores.items():
                # Base allocation from performance
                base_allocation = score / total_score

                # Apply constraints
                allocation = max(self.min_allocation_percentage,
                               min(self.max_allocation_percentage, base_allocation))

                allocations[strategy_type] = allocation

            # Normalize to ensure sum = 1.0
            total_allocation = sum(allocations.values())
            if total_allocation > 0:
                allocations = {k: v / total_allocation for k, v in allocations.items()}

            return allocations

        except Exception as e:
            logger.error(f"Error calculating optimal allocations: {e}")
            # Return equal allocation as fallback
            equal_allocation = 1.0 / len(StrategyType)
            return {strategy: equal_allocation for strategy in StrategyType}

    async def _rebalance_strategies(self, optimal_allocations: Dict[StrategyType, float]):
        """Rebalance strategy allocations if needed"""
        try:
            # Check if rebalancing is needed
            if self.optimization_state.rebalance_urgency < 0.5:
                return  # No urgent need to rebalance

            # Calculate allocation changes
            allocation_changes = {}

            for strategy_type, target_allocation in optimal_allocations.items():
                current_allocation = self.current_allocations.get(strategy_type, 0.2)  # Default 20%
                change = target_allocation - current_allocation

                if abs(change) > 0.05:  # 5% threshold for rebalancing
                    allocation_changes[strategy_type] = change

            if allocation_changes:
                # Apply allocation changes
                for strategy_type, change in allocation_changes.items():
                    new_allocation = self.current_allocations.get(strategy_type, 0.2) + change
                    self.current_allocations[strategy_type] = max(0.05, min(0.6, new_allocation))

                    # Notify strategy engine of allocation change
                    engine = self.strategy_engines.get(strategy_type)
                    if engine and hasattr(engine, 'update_allocation'):
                        await engine.update_allocation(new_allocation)

                logger.info(f"🔄 [REBALANCE] Rebalanced {len(allocation_changes)} strategies")

                # Log allocation changes
                for strategy_type, change in allocation_changes.items():
                    logger.info(f"  📊 {strategy_type.value}: {change:+.1%} "
                               f"(new: {self.current_allocations[strategy_type]:.1%})")

        except Exception as e:
            logger.error(f"Error rebalancing strategies: {e}")

    async def _update_learning_systems(self):
        """Update learning systems with optimization results"""
        try:
            if not NEURAL_COMPONENTS_AVAILABLE:
                return

            # Update reinforcement learning with global performance
            if self.global_performance_history:
                recent_performance = list(self.global_performance_history)[-5:]
                avg_velocity = np.mean([perf['total_velocity'] for perf in recent_performance])

                reward = avg_velocity * 10000  # Scale reward

                await self.rl_agent.update_with_reward({
                    'optimization_type': 'global_time_efficiency',
                    'reward': reward,
                    'efficiency_score': self.optimization_state.efficiency_score
                })

        except Exception as e:
            logger.error(f"Error updating learning systems: {e}")

    async def _log_optimization_metrics(self):
        """Log optimization performance metrics"""
        try:
            state = self.optimization_state

            logger.info(f"⚡ [TIME-OPTIMIZER] Total Velocity: ${state.total_profit_velocity:.4f}/min, "
                       f"Efficiency: {state.efficiency_score:.1%}, "
                       f"Trend: {state.velocity_trend:+.4f}")

            logger.info(f"🏆 [PERFORMANCE] Best: {state.best_performing_strategy.value}, "
                       f"Worst: {state.worst_performing_strategy.value}, "
                       f"Learning: {state.learning_progress:.1%}")

            # Log current allocations
            if self.current_allocations:
                logger.info("📊 [ALLOCATIONS] Current strategy allocations:")
                for strategy_type, allocation in self.current_allocations.items():
                    logger.info(f"  💼 {strategy_type.value}: {allocation:.1%}")

            # Log individual strategy performance
            for strategy_type in StrategyType:
                if strategy_type in self.strategy_performance:
                    current_perf = self.strategy_performance[strategy_type].get('current')
                    if current_perf:
                        logger.debug(f"📈 [{strategy_type.value.upper()}] "
                                   f"Velocity: ${current_perf.profit_velocity:.4f}/min, "
                                   f"Efficiency: {current_perf.time_efficiency_score:.1%}, "
                                   f"Consistency: {current_perf.velocity_consistency:.1%}")

        except Exception as e:
            logger.error(f"Error logging optimization metrics: {e}")

# Export the main classes
__all__ = [
    'GlobalTimeEfficiencyOptimizer',
    'RealTimeProfitTracker',
    'BayesianTimeOptimizer',
    'MetaLearningSystem',
    'TimePerformanceMetrics',
    'StrategyAllocation',
    'TimeOptimizationState'
]
