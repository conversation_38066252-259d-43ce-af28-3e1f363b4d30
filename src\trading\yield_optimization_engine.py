"""
Yield Optimization Engine with Time-Weighted Returns
====================================================

AI system that allocates assets based on time-adjusted yield metrics, predictive
models for shortest-path-to-profit opportunities, self-improving optimizer for
reduced time to profitability, automatic compounding schedules, and dynamic
reallocation to fastest-performing strategies.

THE GOLDEN RULE: MAXIMUM PROFIT IN MINIMUM TIME

Features:
- Time-adjusted yield metrics allocation for optimal capital efficiency
- Predictive models that prioritize shortest-path-to-profit opportunities
- Self-improving optimizer that continuously reduces time to profitability
- Automatic compounding schedules optimized for exponential growth
- Dynamic reallocation that shifts capital to fastest-performing strategies
- Real-time yield monitoring and opportunity detection
- Risk-adjusted return optimization with time decay factors
- Multi-asset portfolio optimization for maximum velocity
"""

import asyncio
import logging
import time
import numpy as np
import pandas as pd
from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, field
from enum import Enum
import json
import math
from collections import defaultdict, deque

# Import neural network components
try:
    from ..neural.enhanced_profit_predictor import EnhancedProfitPredictor
    from ..neural.reinforcement_learning import ReinforcementLearningAgent
    from ..neural.lstm_processor import LSTMProcessor
    from ..neural.transformer_trading_model import TransformerTradingModel
    from ..neural.temporal_intelligence import AdvancedTemporalIntelligence
    NEURAL_COMPONENTS_AVAILABLE = True
except ImportError:
    NEURAL_COMPONENTS_AVAILABLE = False

# Import performance optimization
try:
    from ..performance.speed_optimizer import fast_api_call, cached_market_data
    PERFORMANCE_OPTIMIZATIONS_AVAILABLE = True
except ImportError:
    PERFORMANCE_OPTIMIZATIONS_AVAILABLE = False

logger = logging.getLogger(__name__)

class YieldStrategy(Enum):
    """Yield optimization strategies"""
    STAKING = "staking"
    LENDING = "lending"
    LIQUIDITY_MINING = "liquidity_mining"
    YIELD_FARMING = "yield_farming"
    ARBITRAGE_YIELD = "arbitrage_yield"
    COMPOUND_INTEREST = "compound_interest"
    CROSS_CHAIN_YIELD = "cross_chain_yield"

class RiskLevel(Enum):
    """Risk levels for yield strategies"""
    CONSERVATIVE = "conservative"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"
    SPECULATIVE = "speculative"

class AllocationStatus(Enum):
    """Allocation status"""
    ACTIVE = "active"
    PENDING = "pending"
    REBALANCING = "rebalancing"
    EXITING = "exiting"
    COMPLETED = "completed"

@dataclass
class YieldOpportunity:
    """Yield opportunity data structure"""
    strategy: YieldStrategy
    asset: str
    platform: str
    apy: float  # Annual Percentage Yield
    time_weighted_apy: float  # APY adjusted for time to profit
    min_investment: float
    max_investment: float
    lock_period: int  # Days
    time_to_profit: float  # Expected time to first profit (minutes)
    profit_per_minute: float  # Expected profit per minute
    risk_level: RiskLevel
    confidence: float
    liquidity_score: float  # How quickly can we exit (0-1)
    compound_frequency: int  # Compounding frequency per year
    fees: float  # Platform fees percentage
    created_at: float = field(default_factory=time.time)

@dataclass
class YieldAllocation:
    """Yield allocation tracking"""
    opportunity: YieldOpportunity
    allocated_amount: float
    entry_time: float
    current_value: float
    earned_yield: float
    compound_count: int
    last_compound: float
    next_compound: float
    status: AllocationStatus
    exit_time: Optional[float] = None
    realized_profit: float = 0.0
    time_to_first_profit: Optional[float] = None

@dataclass
class PortfolioMetrics:
    """Portfolio performance metrics"""
    total_value: float
    total_yield_earned: float
    weighted_apy: float
    time_weighted_return: float
    profit_velocity: float  # Profit per unit time
    risk_adjusted_return: float
    diversification_score: float
    liquidity_ratio: float
    compound_efficiency: float

class TimeWeightedYieldCalculator:
    """Calculator for time-weighted yield metrics"""

    def __init__(self):
        self.yield_history = defaultdict(deque)
        self.time_decay_factor = 0.95  # Preference for faster profits

    def calculate_time_weighted_apy(self, base_apy: float, time_to_profit: float,
                                  lock_period: int = 0) -> float:
        """Calculate time-weighted APY that prioritizes speed"""
        try:
            # Base time weight (faster = better)
            time_weight = self._calculate_time_weight(time_to_profit)

            # Lock period penalty
            lock_penalty = self._calculate_lock_penalty(lock_period)

            # Compound frequency bonus
            compound_bonus = 1.0  # Would be calculated based on frequency

            # Calculate time-weighted APY
            time_weighted_apy = base_apy * time_weight * lock_penalty * compound_bonus

            return time_weighted_apy

        except Exception as e:
            logger.error(f"Error calculating time-weighted APY: {e}")
            return base_apy

    def _calculate_time_weight(self, time_to_profit: float) -> float:
        """Calculate time weight factor (faster = higher weight)"""
        try:
            # Convert minutes to hours for calculation
            hours_to_profit = time_to_profit / 60

            # Exponential decay - heavily favor faster profits
            if hours_to_profit <= 1:
                return 2.0  # 2x weight for profits within 1 hour
            elif hours_to_profit <= 24:
                return 1.5  # 1.5x weight for profits within 1 day
            elif hours_to_profit <= 168:  # 1 week
                return 1.2  # 1.2x weight for profits within 1 week
            else:
                return 0.8  # Penalty for very slow profits

        except Exception as e:
            logger.error(f"Error calculating time weight: {e}")
            return 1.0

    def _calculate_lock_penalty(self, lock_period: int) -> float:
        """Calculate penalty for lock periods"""
        try:
            if lock_period == 0:
                return 1.0  # No penalty for no lock
            elif lock_period <= 7:
                return 0.95  # Small penalty for short locks
            elif lock_period <= 30:
                return 0.9   # Medium penalty for monthly locks
            elif lock_period <= 90:
                return 0.8   # Larger penalty for quarterly locks
            else:
                return 0.7   # Heavy penalty for long locks

        except Exception as e:
            logger.error(f"Error calculating lock penalty: {e}")
            return 1.0

    def calculate_profit_velocity(self, yield_earned: float, time_elapsed: float) -> float:
        """Calculate profit velocity (profit per unit time)"""
        try:
            if time_elapsed <= 0:
                return 0.0

            # Profit per minute
            velocity = yield_earned / (time_elapsed / 60)

            return velocity

        except Exception as e:
            logger.error(f"Error calculating profit velocity: {e}")
            return 0.0

class YieldOpportunityScanner:
    """Scanner for yield opportunities across platforms"""

    def __init__(self):
        self.opportunity_cache = {}
        self.platform_apis = {}  # Would contain API clients for various platforms
        self.scan_history = defaultdict(deque)

    async def scan_yield_opportunities(self, available_balance: float) -> List[YieldOpportunity]:
        """Scan for yield opportunities across all platforms"""
        opportunities = []

        try:
            # 1. Staking opportunities
            staking_opportunities = await self._scan_staking_opportunities(available_balance)
            opportunities.extend(staking_opportunities)

            # 2. Lending opportunities
            lending_opportunities = await self._scan_lending_opportunities(available_balance)
            opportunities.extend(lending_opportunities)

            # 3. Liquidity mining opportunities
            liquidity_opportunities = await self._scan_liquidity_mining(available_balance)
            opportunities.extend(liquidity_opportunities)

            # 4. Yield farming opportunities
            farming_opportunities = await self._scan_yield_farming(available_balance)
            opportunities.extend(farming_opportunities)

            # 5. Cross-chain yield opportunities
            cross_chain_opportunities = await self._scan_cross_chain_yield(available_balance)
            opportunities.extend(cross_chain_opportunities)

            # Calculate time-weighted metrics for all opportunities
            calculator = TimeWeightedYieldCalculator()

            for opportunity in opportunities:
                opportunity.time_weighted_apy = calculator.calculate_time_weighted_apy(
                    opportunity.apy, opportunity.time_to_profit, opportunity.lock_period
                )

                opportunity.profit_per_minute = (
                    available_balance * opportunity.time_weighted_apy / 365 / 24 / 60
                )

            # Sort by time-weighted APY
            opportunities.sort(key=lambda x: x.time_weighted_apy, reverse=True)

            logger.info(f"🔍 [YIELD-SCAN] Found {len(opportunities)} yield opportunities")
            return opportunities

        except Exception as e:
            logger.error(f"Error scanning yield opportunities: {e}")
            return []

    async def _scan_staking_opportunities(self, balance: float) -> List[YieldOpportunity]:
        """Scan for staking opportunities"""
        opportunities = []

        try:
            # Simulated staking opportunities (would integrate with real APIs)
            staking_data = [
                {
                    'asset': 'ETH',
                    'platform': 'Ethereum 2.0',
                    'apy': 0.05,  # 5% APY
                    'min_investment': 32,  # 32 ETH minimum
                    'lock_period': 0,  # No lock period
                    'time_to_profit': 24 * 60,  # 24 hours to first reward
                    'risk_level': RiskLevel.CONSERVATIVE,
                    'liquidity_score': 0.3,  # Low liquidity due to lock
                    'fees': 0.0
                },
                {
                    'asset': 'SOL',
                    'platform': 'Solana',
                    'apy': 0.07,  # 7% APY
                    'min_investment': 1,
                    'lock_period': 0,
                    'time_to_profit': 2 * 24 * 60,  # 2 days
                    'risk_level': RiskLevel.MODERATE,
                    'liquidity_score': 0.8,  # High liquidity
                    'fees': 0.001
                }
            ]

            for data in staking_data:
                if balance >= data['min_investment']:
                    opportunity = YieldOpportunity(
                        strategy=YieldStrategy.STAKING,
                        asset=data['asset'],
                        platform=data['platform'],
                        apy=data['apy'],
                        time_weighted_apy=0.0,  # Will be calculated
                        min_investment=data['min_investment'],
                        max_investment=balance,
                        lock_period=data['lock_period'],
                        time_to_profit=data['time_to_profit'],
                        profit_per_minute=0.0,  # Will be calculated
                        risk_level=data['risk_level'],
                        confidence=0.9,  # High confidence for established staking
                        liquidity_score=data['liquidity_score'],
                        compound_frequency=365,  # Daily compounding
                        fees=data['fees']
                    )
                    opportunities.append(opportunity)

            return opportunities

        except Exception as e:
            logger.error(f"Error scanning staking opportunities: {e}")
            return []

    async def _scan_lending_opportunities(self, balance: float) -> List[YieldOpportunity]:
        """Scan for lending opportunities"""
        opportunities = []

        try:
            # Simulated lending opportunities
            lending_data = [
                {
                    'asset': 'USDT',
                    'platform': 'Aave',
                    'apy': 0.03,  # 3% APY
                    'min_investment': 10,
                    'lock_period': 0,
                    'time_to_profit': 60,  # 1 hour to first interest
                    'risk_level': RiskLevel.CONSERVATIVE,
                    'liquidity_score': 0.95,  # Very high liquidity
                    'fees': 0.0
                },
                {
                    'asset': 'USDC',
                    'platform': 'Compound',
                    'apy': 0.025,  # 2.5% APY
                    'min_investment': 1,
                    'lock_period': 0,
                    'time_to_profit': 30,  # 30 minutes
                    'risk_level': RiskLevel.CONSERVATIVE,
                    'liquidity_score': 0.98,
                    'fees': 0.0
                }
            ]

            for data in lending_data:
                if balance >= data['min_investment']:
                    opportunity = YieldOpportunity(
                        strategy=YieldStrategy.LENDING,
                        asset=data['asset'],
                        platform=data['platform'],
                        apy=data['apy'],
                        time_weighted_apy=0.0,
                        min_investment=data['min_investment'],
                        max_investment=balance * 0.5,  # Max 50% in lending
                        lock_period=data['lock_period'],
                        time_to_profit=data['time_to_profit'],
                        profit_per_minute=0.0,
                        risk_level=data['risk_level'],
                        confidence=0.85,
                        liquidity_score=data['liquidity_score'],
                        compound_frequency=365,  # Continuous compounding
                        fees=data['fees']
                    )
                    opportunities.append(opportunity)

            return opportunities

        except Exception as e:
            logger.error(f"Error scanning lending opportunities: {e}")
            return []

    async def _scan_liquidity_mining(self, balance: float) -> List[YieldOpportunity]:
        """Scan for liquidity mining opportunities"""
        opportunities = []

        try:
            # Simulated liquidity mining opportunities
            liquidity_data = [
                {
                    'asset': 'ETH-USDT',
                    'platform': 'Uniswap V3',
                    'apy': 0.15,  # 15% APY
                    'min_investment': 100,
                    'lock_period': 0,
                    'time_to_profit': 6 * 60,  # 6 hours
                    'risk_level': RiskLevel.MODERATE,
                    'liquidity_score': 0.7,
                    'fees': 0.003  # 0.3% fees
                }
            ]

            for data in liquidity_data:
                if balance >= data['min_investment']:
                    opportunity = YieldOpportunity(
                        strategy=YieldStrategy.LIQUIDITY_MINING,
                        asset=data['asset'],
                        platform=data['platform'],
                        apy=data['apy'],
                        time_weighted_apy=0.0,
                        min_investment=data['min_investment'],
                        max_investment=balance * 0.3,  # Max 30% in liquidity mining
                        lock_period=data['lock_period'],
                        time_to_profit=data['time_to_profit'],
                        profit_per_minute=0.0,
                        risk_level=data['risk_level'],
                        confidence=0.7,  # Medium confidence due to impermanent loss risk
                        liquidity_score=data['liquidity_score'],
                        compound_frequency=52,  # Weekly compounding
                        fees=data['fees']
                    )
                    opportunities.append(opportunity)

            return opportunities

        except Exception as e:
            logger.error(f"Error scanning liquidity mining opportunities: {e}")
            return []

    async def _scan_yield_farming(self, balance: float) -> List[YieldOpportunity]:
        """Scan for yield farming opportunities"""
        opportunities = []

        try:
            # Simulated yield farming opportunities
            farming_data = [
                {
                    'asset': 'CAKE',
                    'platform': 'PancakeSwap',
                    'apy': 0.25,  # 25% APY
                    'min_investment': 50,
                    'lock_period': 30,  # 30 days lock
                    'time_to_profit': 24 * 60,  # 24 hours
                    'risk_level': RiskLevel.AGGRESSIVE,
                    'liquidity_score': 0.4,
                    'fees': 0.005
                }
            ]

            for data in farming_data:
                if balance >= data['min_investment']:
                    opportunity = YieldOpportunity(
                        strategy=YieldStrategy.YIELD_FARMING,
                        asset=data['asset'],
                        platform=data['platform'],
                        apy=data['apy'],
                        time_weighted_apy=0.0,
                        min_investment=data['min_investment'],
                        max_investment=balance * 0.2,  # Max 20% in yield farming
                        lock_period=data['lock_period'],
                        time_to_profit=data['time_to_profit'],
                        profit_per_minute=0.0,
                        risk_level=data['risk_level'],
                        confidence=0.6,  # Lower confidence due to token risk
                        liquidity_score=data['liquidity_score'],
                        compound_frequency=12,  # Monthly compounding
                        fees=data['fees']
                    )
                    opportunities.append(opportunity)

            return opportunities

        except Exception as e:
            logger.error(f"Error scanning yield farming opportunities: {e}")
            return []

    async def _scan_cross_chain_yield(self, balance: float) -> List[YieldOpportunity]:
        """Scan for cross-chain yield opportunities"""
        opportunities = []

        try:
            # Simulated cross-chain opportunities
            cross_chain_data = [
                {
                    'asset': 'USDC',
                    'platform': 'Terra Anchor',
                    'apy': 0.18,  # 18% APY
                    'min_investment': 100,
                    'lock_period': 0,
                    'time_to_profit': 2 * 60,  # 2 hours (including bridge time)
                    'risk_level': RiskLevel.SPECULATIVE,
                    'liquidity_score': 0.5,
                    'fees': 0.01  # Bridge fees
                }
            ]

            for data in cross_chain_data:
                if balance >= data['min_investment']:
                    opportunity = YieldOpportunity(
                        strategy=YieldStrategy.CROSS_CHAIN_YIELD,
                        asset=data['asset'],
                        platform=data['platform'],
                        apy=data['apy'],
                        time_weighted_apy=0.0,
                        min_investment=data['min_investment'],
                        max_investment=balance * 0.15,  # Max 15% in cross-chain
                        lock_period=data['lock_period'],
                        time_to_profit=data['time_to_profit'],
                        profit_per_minute=0.0,
                        risk_level=data['risk_level'],
                        confidence=0.5,  # Lower confidence due to bridge risks
                        liquidity_score=data['liquidity_score'],
                        compound_frequency=365,
                        fees=data['fees']
                    )
                    opportunities.append(opportunity)

            return opportunities

        except Exception as e:
            logger.error(f"Error scanning cross-chain yield opportunities: {e}")
            return []

class YieldOptimizationEngine:
    """
    Main yield optimization engine with time-weighted returns
    """

    def __init__(self, exchange_clients: Dict, config: Dict = None):
        self.exchange_clients = exchange_clients
        self.config = config or {}

        # Initialize components
        self.opportunity_scanner = YieldOpportunityScanner()
        self.yield_calculator = TimeWeightedYieldCalculator()

        # Initialize neural components if available
        if NEURAL_COMPONENTS_AVAILABLE:
            self.profit_predictor = EnhancedProfitPredictor()
            self.rl_agent = ReinforcementLearningAgent()

        # Trading state
        self.active_allocations = {}
        self.portfolio_metrics = PortfolioMetrics(
            total_value=0.0,
            total_yield_earned=0.0,
            weighted_apy=0.0,
            time_weighted_return=0.0,
            profit_velocity=0.0,
            risk_adjusted_return=0.0,
            diversification_score=0.0,
            liquidity_ratio=0.0,
            compound_efficiency=0.0
        )
        self.performance_history = defaultdict(deque)

        # Configuration
        self.max_allocations = self.config.get('max_allocations', 5)
        self.rebalance_interval = self.config.get('rebalance_interval', 3600)  # 1 hour
        self.min_allocation_amount = self.config.get('min_allocation_amount', 10)
        self.max_risk_per_allocation = self.config.get('max_risk_per_allocation', 0.2)

        logger.info("💰 [YIELD-OPTIMIZATION] Yield Optimization Engine initialized")

    async def start_optimization(self):
        """Start the main yield optimization loop"""
        logger.info("🎯 [YIELD-OPTIMIZATION] Starting yield optimization engine...")

        try:
            while True:
                start_time = time.time()

                # 1. Get available balance
                available_balance = await self._get_available_balance()

                # 2. Scan for yield opportunities
                opportunities = await self.opportunity_scanner.scan_yield_opportunities(available_balance)

                # 3. Optimize allocation
                optimal_allocations = await self._optimize_allocations(opportunities, available_balance)

                # 4. Execute allocations
                await self._execute_allocations(optimal_allocations)

                # 5. Manage existing allocations
                await self._manage_allocations()

                # 6. Update portfolio metrics
                await self._update_portfolio_metrics()

                # 7. Compound yields
                await self._compound_yields()

                # 8. Update learning systems
                await self._update_learning_systems()

                # 9. Log performance metrics
                await self._log_performance_metrics()

                # Calculate loop time and sleep
                loop_time = time.time() - start_time
                target_loop_time = 300  # 5 minutes for yield optimization

                if loop_time < target_loop_time:
                    await asyncio.sleep(target_loop_time - loop_time)

                logger.debug(f"⚡ [YIELD-OPTIMIZATION] Optimization loop completed in {loop_time:.2f}s")

        except Exception as e:
            logger.error(f"❌ [YIELD-OPTIMIZATION] Optimization engine error: {e}")
            raise

    async def _get_available_balance(self) -> float:
        """Get available balance for yield optimization"""
        try:
            # Get USDT balance from primary exchange
            bybit_client = self.exchange_clients.get('bybit')
            if bybit_client:
                balance = await bybit_client.get_balance("USDT")
                return float(balance)

            return 0.0

        except Exception as e:
            logger.error(f"Error getting available balance: {e}")
            return 0.0

    async def _optimize_allocations(self, opportunities: List[YieldOpportunity],
                                  available_balance: float) -> List[Tuple[YieldOpportunity, float]]:
        """Optimize allocation across yield opportunities"""
        try:
            if not opportunities or available_balance < self.min_allocation_amount:
                return []

            # Filter opportunities by minimum investment
            viable_opportunities = [
                opp for opp in opportunities
                if opp.min_investment <= available_balance
            ]

            if not viable_opportunities:
                return []

            # Use neural optimization if available
            if NEURAL_COMPONENTS_AVAILABLE:
                allocations = await self._neural_allocation_optimization(
                    viable_opportunities, available_balance
                )
            else:
                allocations = await self._greedy_allocation_optimization(
                    viable_opportunities, available_balance
                )

            return allocations

        except Exception as e:
            logger.error(f"Error optimizing allocations: {e}")
            return []

    async def _neural_allocation_optimization(self, opportunities: List[YieldOpportunity],
                                            available_balance: float) -> List[Tuple[YieldOpportunity, float]]:
        """Use neural networks for allocation optimization"""
        try:
            # Prepare features for each opportunity
            features = []
            for opp in opportunities:
                feature_vector = [
                    opp.time_weighted_apy,
                    opp.profit_per_minute,
                    opp.confidence,
                    opp.liquidity_score,
                    1.0 / (opp.lock_period + 1),  # Inverse lock period
                    opp.risk_level.value == 'conservative',  # Risk encoding
                    opp.risk_level.value == 'moderate',
                    opp.risk_level.value == 'aggressive',
                    opp.compound_frequency / 365,  # Normalized frequency
                    1.0 - opp.fees  # Fee efficiency
                ]
                features.append(feature_vector)

            # Use transformer model for allocation weights
            allocation_weights = await self.transformer_model.predict_allocation_weights({
                'features': np.array(features),
                'available_balance': available_balance,
                'num_opportunities': len(opportunities)
            })

            # Convert weights to actual allocations
            allocations = []
            remaining_balance = available_balance

            for i, (opp, weight) in enumerate(zip(opportunities, allocation_weights)):
                if remaining_balance < opp.min_investment:
                    continue

                # Calculate allocation amount
                allocation_amount = min(
                    remaining_balance * weight,
                    opp.max_investment,
                    remaining_balance * self.max_risk_per_allocation
                )

                if allocation_amount >= opp.min_investment:
                    allocations.append((opp, allocation_amount))
                    remaining_balance -= allocation_amount

                # Limit number of allocations
                if len(allocations) >= self.max_allocations:
                    break

            return allocations

        except Exception as e:
            logger.error(f"Error in neural allocation optimization: {e}")
            return await self._greedy_allocation_optimization(opportunities, available_balance)

    async def _greedy_allocation_optimization(self, opportunities: List[YieldOpportunity],
                                            available_balance: float) -> List[Tuple[YieldOpportunity, float]]:
        """Greedy allocation optimization fallback"""
        try:
            allocations = []
            remaining_balance = available_balance

            # Sort by time-weighted APY (already sorted)
            for opp in opportunities[:self.max_allocations]:
                if remaining_balance < opp.min_investment:
                    continue

                # Allocate based on opportunity quality and risk
                base_allocation = remaining_balance * 0.2  # 20% base allocation

                # Adjust for confidence and liquidity
                confidence_factor = opp.confidence
                liquidity_factor = opp.liquidity_score

                allocation_amount = base_allocation * confidence_factor * liquidity_factor

                # Apply constraints
                allocation_amount = max(opp.min_investment,
                                      min(allocation_amount, opp.max_investment))
                allocation_amount = min(allocation_amount, remaining_balance)

                if allocation_amount >= opp.min_investment:
                    allocations.append((opp, allocation_amount))
                    remaining_balance -= allocation_amount

            return allocations

        except Exception as e:
            logger.error(f"Error in greedy allocation optimization: {e}")
            return []

    async def _execute_allocations(self, allocations: List[Tuple[YieldOpportunity, float]]):
        """Execute yield allocations"""
        try:
            for opportunity, amount in allocations:
                try:
                    # Check if we already have this allocation
                    allocation_key = f"{opportunity.strategy.value}_{opportunity.asset}_{opportunity.platform}"

                    if allocation_key in self.active_allocations:
                        continue  # Skip if already allocated

                    # Execute the allocation
                    success = await self._execute_single_allocation(opportunity, amount)

                    if success:
                        # Create allocation tracking
                        allocation = YieldAllocation(
                            opportunity=opportunity,
                            allocated_amount=amount,
                            entry_time=time.time(),
                            current_value=amount,
                            earned_yield=0.0,
                            compound_count=0,
                            last_compound=time.time(),
                            next_compound=time.time() + (365 * 24 * 3600 / opportunity.compound_frequency),
                            status=AllocationStatus.ACTIVE
                        )

                        self.active_allocations[allocation_key] = allocation

                        logger.info(f"✅ [ALLOCATE] Allocated ${amount:.2f} to {opportunity.strategy.value} "
                                   f"on {opportunity.platform} (APY: {opportunity.time_weighted_apy:.1%})")

                except Exception as e:
                    logger.error(f"Error executing allocation: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error executing allocations: {e}")

    async def _execute_single_allocation(self, opportunity: YieldOpportunity, amount: float) -> bool:
        """Execute a single yield allocation"""
        try:
            # This would integrate with actual yield platforms
            # For now, simulate successful allocation

            logger.info(f"🔄 [EXECUTE] Executing {opportunity.strategy.value} allocation: "
                       f"${amount:.2f} on {opportunity.platform}")

            # Simulate allocation delay
            await asyncio.sleep(0.1)

            return True

        except Exception as e:
            logger.error(f"Error executing single allocation: {e}")
            return False

    async def _manage_allocations(self):
        """Manage existing yield allocations"""
        try:
            allocations_to_exit = []

            for allocation_key, allocation in self.active_allocations.items():
                try:
                    # Update allocation value and yield
                    await self._update_allocation_value(allocation)

                    # Check exit conditions
                    should_exit, reason = await self._should_exit_allocation(allocation)

                    if should_exit:
                        allocations_to_exit.append((allocation_key, reason))

                except Exception as e:
                    logger.error(f"Error managing allocation {allocation_key}: {e}")
                    continue

            # Exit allocations that meet exit criteria
            for allocation_key, reason in allocations_to_exit:
                await self._exit_allocation(allocation_key, reason)

        except Exception as e:
            logger.error(f"Error managing allocations: {e}")

    async def _update_allocation_value(self, allocation: YieldAllocation):
        """Update allocation current value and earned yield"""
        try:
            # Calculate time elapsed
            time_elapsed = time.time() - allocation.entry_time
            hours_elapsed = time_elapsed / 3600

            # Calculate earned yield
            annual_yield = allocation.allocated_amount * allocation.opportunity.apy
            earned_yield = annual_yield * (hours_elapsed / (365 * 24))

            # Apply fees
            net_yield = earned_yield * (1 - allocation.opportunity.fees)

            # Update allocation
            allocation.earned_yield = net_yield
            allocation.current_value = allocation.allocated_amount + net_yield

            # Track time to first profit
            if allocation.time_to_first_profit is None and net_yield > 0:
                allocation.time_to_first_profit = time_elapsed / 60  # minutes

        except Exception as e:
            logger.error(f"Error updating allocation value: {e}")

    async def _should_exit_allocation(self, allocation: YieldAllocation) -> Tuple[bool, str]:
        """Determine if allocation should be exited"""
        try:
            # 1. Lock period check
            if allocation.opportunity.lock_period > 0:
                time_elapsed_days = (time.time() - allocation.entry_time) / (24 * 3600)
                if time_elapsed_days < allocation.opportunity.lock_period:
                    return False, ""  # Still locked

            # 2. Profit target reached
            profit_percentage = allocation.earned_yield / allocation.allocated_amount
            if profit_percentage >= 0.1:  # 10% profit target
                return True, f"Profit target reached: {profit_percentage:.1%}"

            # 3. Better opportunities available
            current_time_weighted_apy = allocation.opportunity.time_weighted_apy

            # Get current opportunities
            available_balance = await self._get_available_balance()
            current_opportunities = await self.opportunity_scanner.scan_yield_opportunities(available_balance)

            if current_opportunities:
                best_current_apy = current_opportunities[0].time_weighted_apy

                # Exit if significantly better opportunity exists
                if best_current_apy > current_time_weighted_apy * 1.5:  # 50% better
                    return True, f"Better opportunity available: {best_current_apy:.1%} vs {current_time_weighted_apy:.1%}"

            # 4. Risk management - platform issues
            # Would check platform health, smart contract risks, etc.

            return False, ""

        except Exception as e:
            logger.error(f"Error checking allocation exit conditions: {e}")
            return False, "Error in exit condition check"

    async def _exit_allocation(self, allocation_key: str, reason: str):
        """Exit a yield allocation"""
        try:
            if allocation_key not in self.active_allocations:
                return

            allocation = self.active_allocations[allocation_key]

            # Execute exit (would integrate with actual platforms)
            logger.info(f"🔒 [EXIT] Exiting allocation {allocation_key}: "
                       f"Profit=${allocation.earned_yield:.2f} - {reason}")

            # Update allocation status
            allocation.status = AllocationStatus.COMPLETED
            allocation.exit_time = time.time()
            allocation.realized_profit = allocation.earned_yield

            # Update performance history
            duration_hours = (allocation.exit_time - allocation.entry_time) / 3600
            profit_velocity = self.yield_calculator.calculate_profit_velocity(
                allocation.earned_yield, allocation.exit_time - allocation.entry_time
            )

            self.performance_history[allocation.opportunity.strategy.value].append({
                'profit': allocation.earned_yield,
                'duration_hours': duration_hours,
                'profit_velocity': profit_velocity,
                'apy_achieved': (allocation.earned_yield / allocation.allocated_amount) * (365 * 24 / duration_hours),
                'reason': reason,
                'timestamp': time.time()
            })

            # Remove from active allocations
            del self.active_allocations[allocation_key]

        except Exception as e:
            logger.error(f"Error exiting allocation {allocation_key}: {e}")

    async def _update_portfolio_metrics(self):
        """Update portfolio performance metrics"""
        try:
            if not self.active_allocations:
                return

            # Calculate total values
            total_allocated = sum(alloc.allocated_amount for alloc in self.active_allocations.values())
            total_current_value = sum(alloc.current_value for alloc in self.active_allocations.values())
            total_yield_earned = sum(alloc.earned_yield for alloc in self.active_allocations.values())

            # Calculate weighted APY
            weighted_apy = 0.0
            if total_allocated > 0:
                for allocation in self.active_allocations.values():
                    weight = allocation.allocated_amount / total_allocated
                    weighted_apy += allocation.opportunity.time_weighted_apy * weight

            # Calculate time-weighted return
            total_time_elapsed = 0
            total_weighted_return = 0

            for allocation in self.active_allocations.values():
                time_elapsed = time.time() - allocation.entry_time
                if time_elapsed > 0:
                    allocation_return = allocation.earned_yield / allocation.allocated_amount
                    time_weight = allocation.allocated_amount / total_allocated
                    total_weighted_return += allocation_return * time_weight
                    total_time_elapsed += time_elapsed * time_weight

            # Calculate profit velocity
            profit_velocity = 0.0
            if total_time_elapsed > 0:
                profit_velocity = total_yield_earned / (total_time_elapsed / 60)  # Per minute

            # Update portfolio metrics
            self.portfolio_metrics.total_value = total_current_value
            self.portfolio_metrics.total_yield_earned = total_yield_earned
            self.portfolio_metrics.weighted_apy = weighted_apy
            self.portfolio_metrics.time_weighted_return = total_weighted_return
            self.portfolio_metrics.profit_velocity = profit_velocity

            # Calculate diversification score
            strategy_counts = defaultdict(int)
            for allocation in self.active_allocations.values():
                strategy_counts[allocation.opportunity.strategy.value] += 1

            if len(strategy_counts) > 0:
                self.portfolio_metrics.diversification_score = len(strategy_counts) / len(YieldStrategy)

            # Calculate liquidity ratio
            liquid_value = sum(
                alloc.current_value for alloc in self.active_allocations.values()
                if alloc.opportunity.liquidity_score > 0.8
            )
            self.portfolio_metrics.liquidity_ratio = liquid_value / total_current_value if total_current_value > 0 else 0

        except Exception as e:
            logger.error(f"Error updating portfolio metrics: {e}")

    async def _compound_yields(self):
        """Compound yields for eligible allocations"""
        try:
            current_time = time.time()

            for allocation in self.active_allocations.values():
                if (allocation.opportunity.compound_frequency > 0 and
                    current_time >= allocation.next_compound):

                    # Compound the yield
                    compound_amount = allocation.earned_yield
                    allocation.allocated_amount += compound_amount
                    allocation.earned_yield = 0.0  # Reset earned yield
                    allocation.compound_count += 1
                    allocation.last_compound = current_time

                    # Calculate next compound time
                    compound_interval = 365 * 24 * 3600 / allocation.opportunity.compound_frequency
                    allocation.next_compound = current_time + compound_interval

                    logger.info(f"🔄 [COMPOUND] Compounded ${compound_amount:.2f} for "
                               f"{allocation.opportunity.strategy.value} (Count: {allocation.compound_count})")

        except Exception as e:
            logger.error(f"Error compounding yields: {e}")

    async def _update_learning_systems(self):
        """Update learning systems with yield performance"""
        try:
            if not NEURAL_COMPONENTS_AVAILABLE:
                return

            # Update with recent performance
            for strategy, performance_data in self.performance_history.items():
                if performance_data:
                    recent_performance = list(performance_data)[-5:]  # Last 5 allocations
                    avg_profit_velocity = np.mean([p['profit_velocity'] for p in recent_performance])

                    # Update reinforcement learning
                    reward = avg_profit_velocity * 1000  # Scale reward

                    await self.rl_agent.update_with_reward({
                        'strategy': strategy,
                        'reward': reward
                    })

        except Exception as e:
            logger.error(f"Error updating learning systems: {e}")

    async def _log_performance_metrics(self):
        """Log performance metrics"""
        try:
            metrics = self.portfolio_metrics

            logger.info(f"💰 [YIELD-PERFORMANCE] Portfolio Value: ${metrics.total_value:.2f}, "
                       f"Yield Earned: ${metrics.total_yield_earned:.2f}, "
                       f"Weighted APY: {metrics.weighted_apy:.1%}")

            logger.info(f"📊 [YIELD-METRICS] Profit Velocity: ${metrics.profit_velocity:.4f}/min, "
                       f"Diversification: {metrics.diversification_score:.1%}, "
                       f"Liquidity Ratio: {metrics.liquidity_ratio:.1%}")

            # Log active allocations
            if self.active_allocations:
                logger.info(f"🔄 [ACTIVE-ALLOCATIONS] Count: {len(self.active_allocations)}")
                for key, allocation in self.active_allocations.items():
                    profit_pct = allocation.earned_yield / allocation.allocated_amount * 100
                    logger.debug(f"  📈 {key}: ${allocation.current_value:.2f} (+{profit_pct:.2f}%)")

        except Exception as e:
            logger.error(f"Error logging performance metrics: {e}")

# Export the main classes
__all__ = ['YieldOptimizationEngine', 'YieldOpportunity', 'YieldAllocation', 'YieldStrategy']

    async def _is_asset_idle(self, currency: str, balance: Decimal) -> bool:
        """Determine if an asset is idle and available for yield optimization"""
        try:
            # Check minimum thresholds
            min_threshold = self.config.get('min_yield_threshold', {}).get(currency, 10.0)  # $10 default
            usd_value = await self._get_usd_value(currency, balance)
            
            if usd_value < min_threshold:
                return False
            
            # Check if asset is in active trading positions
            # This would integrate with the trading engine to check active orders
            # For now, assume assets above threshold are idle
            
            # Check if asset is already in yield positions
            if currency in self.active_positions:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error checking if {currency} is idle: {e}")
            return False

    async def _find_yield_opportunities(self, currency: str, balance: Decimal) -> List[YieldOpportunity]:
        """Find available yield opportunities for a specific currency"""
        try:
            opportunities = []
            
            # Check cache first
            cache_key = f"{currency}_{balance}"
            if cache_key in self.opportunity_cache:
                cached_data = self.opportunity_cache[cache_key]
                if time.time() - cached_data['timestamp'] < 300:  # 5 minutes cache
                    return cached_data['opportunities']
            
            # Strategy 1: Exchange staking
            staking_ops = await self._find_staking_opportunities(currency, balance)
            opportunities.extend(staking_ops)
            
            # Strategy 2: Lending opportunities
            lending_ops = await self._find_lending_opportunities(currency, balance)
            opportunities.extend(lending_ops)
            
            # Strategy 3: Liquidity mining
            liquidity_ops = await self._find_liquidity_mining_opportunities(currency, balance)
            opportunities.extend(liquidity_ops)
            
            # Strategy 4: Savings accounts
            savings_ops = await self._find_savings_opportunities(currency, balance)
            opportunities.extend(savings_ops)
            
            # Strategy 5: Grid trading for yield
            grid_ops = await self._find_grid_trading_opportunities(currency, balance)
            opportunities.extend(grid_ops)
            
            # Cache results
            self.opportunity_cache[cache_key] = {
                'opportunities': opportunities,
                'timestamp': time.time()
            }
            
            return opportunities
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error finding opportunities for {currency}: {e}")
            return []

    async def _find_staking_opportunities(self, currency: str, balance: Decimal) -> List[YieldOpportunity]:
        """Find staking opportunities for the currency"""
        try:
            opportunities = []
            
            # Define staking opportunities by currency
            staking_data = {
                'ETH': {'apy': 0.045, 'min_amount': 0.01, 'lock_days': 0, 'risk': 'low'},
                'SOL': {'apy': 0.065, 'min_amount': 0.1, 'lock_days': 3, 'risk': 'medium'},
                'ADA': {'apy': 0.055, 'min_amount': 10, 'lock_days': 5, 'risk': 'low'},
                'DOT': {'apy': 0.12, 'min_amount': 1, 'lock_days': 28, 'risk': 'medium'},
                'AVAX': {'apy': 0.085, 'min_amount': 0.5, 'lock_days': 14, 'risk': 'medium'},
                'ATOM': {'apy': 0.15, 'min_amount': 1, 'lock_days': 21, 'risk': 'medium'},
            }
            
            if currency in staking_data:
                data = staking_data[currency]
                
                if balance >= Decimal(str(data['min_amount'])):
                    opportunity = YieldOpportunity(
                        strategy=YieldStrategy.STAKING,
                        currency=currency,
                        platform='exchange_staking',
                        apy=data['apy'],
                        minimum_amount=Decimal(str(data['min_amount'])),
                        lock_period_days=data['lock_days'],
                        risk_level=data['risk'],
                        liquidity_score=0.8 if data['lock_days'] == 0 else 0.6,
                        auto_compound=True,
                        estimated_daily_yield=balance * Decimal(str(data['apy'])) / 365,
                        fees={'entry': 0.0, 'exit': 0.0, 'management': 0.001},
                        requirements=['minimum_balance'],
                        confidence=0.9
                    )
                    opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error finding staking opportunities: {e}")
            return []

    async def _find_lending_opportunities(self, currency: str, balance: Decimal) -> List[YieldOpportunity]:
        """Find lending opportunities for the currency"""
        try:
            opportunities = []
            
            # Define lending opportunities
            lending_data = {
                'USDT': {'apy': 0.08, 'min_amount': 10, 'lock_days': 0, 'risk': 'low'},
                'USDC': {'apy': 0.075, 'min_amount': 10, 'lock_days': 0, 'risk': 'low'},
                'BTC': {'apy': 0.035, 'min_amount': 0.001, 'lock_days': 0, 'risk': 'low'},
                'ETH': {'apy': 0.04, 'min_amount': 0.01, 'lock_days': 0, 'risk': 'low'},
            }
            
            if currency in lending_data:
                data = lending_data[currency]
                
                if balance >= Decimal(str(data['min_amount'])):
                    opportunity = YieldOpportunity(
                        strategy=YieldStrategy.LENDING,
                        currency=currency,
                        platform='exchange_lending',
                        apy=data['apy'],
                        minimum_amount=Decimal(str(data['min_amount'])),
                        lock_period_days=data['lock_days'],
                        risk_level=data['risk'],
                        liquidity_score=0.9,  # Usually high liquidity
                        auto_compound=True,
                        estimated_daily_yield=balance * Decimal(str(data['apy'])) / 365,
                        fees={'entry': 0.0, 'exit': 0.0, 'management': 0.0},
                        requirements=['kyc_verified'],
                        confidence=0.85
                    )
                    opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error finding lending opportunities: {e}")
            return []

    async def _find_liquidity_mining_opportunities(self, currency: str, balance: Decimal) -> List[YieldOpportunity]:
        """Find liquidity mining opportunities"""
        try:
            opportunities = []
            
            # Define liquidity mining pairs and rewards
            liquidity_pairs = {
                'BTC': [
                    {'pair': 'BTC/USDT', 'apy': 0.15, 'min_amount': 0.001, 'risk': 'medium'},
                    {'pair': 'BTC/ETH', 'apy': 0.12, 'min_amount': 0.001, 'risk': 'medium'}
                ],
                'ETH': [
                    {'pair': 'ETH/USDT', 'apy': 0.18, 'min_amount': 0.01, 'risk': 'medium'},
                    {'pair': 'ETH/BTC', 'apy': 0.14, 'min_amount': 0.01, 'risk': 'medium'}
                ],
                'SOL': [
                    {'pair': 'SOL/USDT', 'apy': 0.25, 'min_amount': 0.1, 'risk': 'high'},
                ]
            }
            
            if currency in liquidity_pairs:
                for pair_data in liquidity_pairs[currency]:
                    if balance >= Decimal(str(pair_data['min_amount'])):
                        opportunity = YieldOpportunity(
                            strategy=YieldStrategy.LIQUIDITY_MINING,
                            currency=currency,
                            platform='dex_liquidity',
                            apy=pair_data['apy'],
                            minimum_amount=Decimal(str(pair_data['min_amount'])),
                            lock_period_days=0,
                            risk_level=pair_data['risk'],
                            liquidity_score=0.7,
                            auto_compound=False,
                            estimated_daily_yield=balance * Decimal(str(pair_data['apy'])) / 365,
                            fees={'entry': 0.003, 'exit': 0.003, 'management': 0.0},
                            requirements=['impermanent_loss_risk'],
                            confidence=0.7
                        )
                        opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error finding liquidity mining opportunities: {e}")
            return []

    async def _find_savings_opportunities(self, currency: str, balance: Decimal) -> List[YieldOpportunity]:
        """Find savings account opportunities"""
        try:
            opportunities = []
            
            # Exchange savings products
            savings_data = {
                'USDT': {'apy': 0.05, 'min_amount': 1, 'lock_days': 0, 'risk': 'low'},
                'USDC': {'apy': 0.045, 'min_amount': 1, 'lock_days': 0, 'risk': 'low'},
                'BTC': {'apy': 0.02, 'min_amount': 0.0001, 'lock_days': 0, 'risk': 'low'},
                'ETH': {'apy': 0.025, 'min_amount': 0.001, 'lock_days': 0, 'risk': 'low'},
            }
            
            if currency in savings_data:
                data = savings_data[currency]
                
                if balance >= Decimal(str(data['min_amount'])):
                    opportunity = YieldOpportunity(
                        strategy=YieldStrategy.SAVINGS_ACCOUNT,
                        currency=currency,
                        platform='exchange_savings',
                        apy=data['apy'],
                        minimum_amount=Decimal(str(data['min_amount'])),
                        lock_period_days=data['lock_days'],
                        risk_level=data['risk'],
                        liquidity_score=1.0,  # Instant withdrawal
                        auto_compound=True,
                        estimated_daily_yield=balance * Decimal(str(data['apy'])) / 365,
                        fees={'entry': 0.0, 'exit': 0.0, 'management': 0.0},
                        requirements=['minimum_balance'],
                        confidence=0.95
                    )
                    opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error finding savings opportunities: {e}")
            return []

    async def _find_grid_trading_opportunities(self, currency: str, balance: Decimal) -> List[YieldOpportunity]:
        """Find grid trading opportunities for yield generation"""
        try:
            opportunities = []
            
            # Grid trading works best with volatile but range-bound assets
            grid_suitable = {
                'BTC': {'apy': 0.10, 'min_amount': 0.001, 'volatility': 'medium'},
                'ETH': {'apy': 0.12, 'min_amount': 0.01, 'volatility': 'medium'},
                'SOL': {'apy': 0.18, 'min_amount': 0.1, 'volatility': 'high'},
                'ADA': {'apy': 0.15, 'min_amount': 10, 'volatility': 'medium'},
            }
            
            if currency in grid_suitable:
                data = grid_suitable[currency]
                
                if balance >= Decimal(str(data['min_amount'])):
                    opportunity = YieldOpportunity(
                        strategy=YieldStrategy.GRID_TRADING,
                        currency=currency,
                        platform='exchange_grid',
                        apy=data['apy'],
                        minimum_amount=Decimal(str(data['min_amount'])),
                        lock_period_days=0,
                        risk_level='medium',
                        liquidity_score=0.8,
                        auto_compound=True,
                        estimated_daily_yield=balance * Decimal(str(data['apy'])) / 365,
                        fees={'entry': 0.001, 'exit': 0.001, 'trading': 0.001},
                        requirements=['market_volatility'],
                        confidence=0.75
                    )
                    opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error finding grid trading opportunities: {e}")
            return []

    def _calculate_opportunity_score(self, opportunities: List[YieldOpportunity]) -> float:
        """Calculate overall opportunity score for a currency"""
        try:
            if not opportunities:
                return 0.0
            
            # Weight factors
            weights = {
                'apy': 0.4,
                'liquidity': 0.2,
                'risk': 0.2,
                'confidence': 0.2
            }
            
            scores = []
            for opp in opportunities:
                # Normalize APY (assume max 50% APY)
                apy_score = min(1.0, opp.apy / 0.5)
                
                # Liquidity score (already 0-1)
                liquidity_score = opp.liquidity_score
                
                # Risk score (invert - lower risk is better)
                risk_scores = {'low': 1.0, 'medium': 0.7, 'high': 0.4}
                risk_score = risk_scores.get(opp.risk_level, 0.5)
                
                # Confidence score (already 0-1)
                confidence_score = opp.confidence
                
                # Calculate weighted score
                weighted_score = (
                    apy_score * weights['apy'] +
                    liquidity_score * weights['liquidity'] +
                    risk_score * weights['risk'] +
                    confidence_score * weights['confidence']
                )
                
                scores.append(weighted_score)
            
            # Return best opportunity score
            return max(scores) if scores else 0.0
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error calculating opportunity score: {e}")
            return 0.0

    def _get_recommended_strategy(self, opportunities: List[YieldOpportunity]) -> Optional[YieldOpportunity]:
        """Get the recommended yield strategy from available opportunities"""
        try:
            if not opportunities:
                return None
            
            # Score each opportunity
            scored_opportunities = []
            for opp in opportunities:
                score = self._calculate_single_opportunity_score(opp)
                scored_opportunities.append((score, opp))
            
            # Sort by score (highest first)
            scored_opportunities.sort(key=lambda x: x[0], reverse=True)
            
            return scored_opportunities[0][1] if scored_opportunities else None
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error getting recommended strategy: {e}")
            return None

    def _calculate_single_opportunity_score(self, opportunity: YieldOpportunity) -> float:
        """Calculate score for a single opportunity"""
        try:
            # Base score from APY
            score = opportunity.apy * 100  # Convert to percentage points
            
            # Adjust for risk
            risk_multipliers = {'low': 1.0, 'medium': 0.8, 'high': 0.6}
            score *= risk_multipliers.get(opportunity.risk_level, 0.7)
            
            # Adjust for liquidity
            score *= opportunity.liquidity_score
            
            # Adjust for confidence
            score *= opportunity.confidence
            
            # Penalty for lock periods
            if opportunity.lock_period_days > 0:
                lock_penalty = 1.0 - (opportunity.lock_period_days / 365) * 0.2  # 20% penalty per year
                score *= max(0.5, lock_penalty)
            
            return score
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error calculating single opportunity score: {e}")
            return 0.0

    async def _get_usd_value(self, currency: str, amount: Decimal) -> Decimal:
        """Get USD value of a currency amount"""
        try:
            if currency in ['USD', 'USDT', 'USDC']:
                return amount
            
            # Try to get price from exchange clients
            for client in self.exchange_clients.values():
                if hasattr(client, 'get_price'):
                    symbol = f"{currency}USDT"
                    price = client.get_price(symbol)
                    if price and float(price) > 0:
                        return amount * Decimal(str(price))
            
            return Decimal('0')
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error getting USD value for {currency}: {e}")
            return Decimal('0')

# Export the main class
__all__ = ['AdvancedYieldOptimizer', 'YieldOpportunity', 'YieldPosition', 'YieldStrategy']
