"""
Advanced Yield Optimization Engine for Idle Assets
Maximizes returns on idle cryptocurrency holdings through various yield strategies
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timezone, timedelta
import numpy as np

logger = logging.getLogger(__name__)

class YieldStrategy(Enum):
    """Available yield generation strategies"""
    STAKING = "staking"
    LENDING = "lending"
    LIQUIDITY_MINING = "liquidity_mining"
    YIELD_FARMING = "yield_farming"
    SAVINGS_ACCOUNT = "savings_account"
    DUAL_INVESTMENT = "dual_investment"
    GRID_TRADING = "grid_trading"
    ARBITRAGE_FARMING = "arbitrage_farming"

@dataclass
class YieldOpportunity:
    """Represents a yield generation opportunity"""
    strategy: YieldStrategy
    currency: str
    platform: str
    apy: float
    minimum_amount: Decimal
    lock_period_days: int
    risk_level: str  # low, medium, high
    liquidity_score: float  # 0-1, higher is more liquid
    auto_compound: bool
    estimated_daily_yield: Decimal
    fees: Dict[str, float]
    requirements: List[str]
    confidence: float

@dataclass
class YieldPosition:
    """Represents an active yield position"""
    opportunity: YieldOpportunity
    amount_invested: Decimal
    start_date: datetime
    current_value: Decimal
    accrued_yield: Decimal
    status: str  # active, pending, completed, failed
    position_id: str
    auto_reinvest: bool

class AdvancedYieldOptimizer:
    """Advanced yield optimization engine for idle cryptocurrency assets"""
    
    def __init__(self, exchange_clients: Dict[str, Any], config: Dict = None):
        self.exchange_clients = exchange_clients
        self.config = config or {}
        
        # Yield tracking
        self.active_positions = {}
        self.yield_history = []
        self.opportunity_cache = {}
        
        # Risk management
        self.max_allocation_per_strategy = self.config.get('max_allocation_per_strategy', 0.3)  # 30%
        self.min_liquidity_score = self.config.get('min_liquidity_score', 0.5)
        self.max_lock_period_days = self.config.get('max_lock_period_days', 90)
        
        # Performance tracking
        self.total_yield_earned = Decimal('0')
        self.yield_performance_by_strategy = {}
        
        logger.info("💰 [YIELD] Advanced yield optimization engine initialized")

    async def analyze_idle_assets(self, balances: Dict[str, Decimal]) -> Dict[str, Any]:
        """Analyze idle assets and identify yield opportunities"""
        try:
            idle_analysis = {}
            total_idle_value = Decimal('0')
            
            for currency, balance in balances.items():
                if balance > 0:
                    # Determine if asset is idle (not in active trades)
                    is_idle = await self._is_asset_idle(currency, balance)
                    
                    if is_idle:
                        # Get USD value
                        usd_value = await self._get_usd_value(currency, balance)
                        
                        # Find yield opportunities
                        opportunities = await self._find_yield_opportunities(currency, balance)
                        
                        # Calculate opportunity score
                        opportunity_score = self._calculate_opportunity_score(opportunities)
                        
                        idle_analysis[currency] = {
                            'balance': balance,
                            'usd_value': usd_value,
                            'is_idle': True,
                            'opportunities': opportunities,
                            'opportunity_score': opportunity_score,
                            'recommended_strategy': self._get_recommended_strategy(opportunities)
                        }
                        
                        total_idle_value += usd_value
                    else:
                        idle_analysis[currency] = {
                            'balance': balance,
                            'is_idle': False,
                            'reason': 'Asset in active trading'
                        }
            
            return {
                'idle_assets': idle_analysis,
                'total_idle_value_usd': total_idle_value,
                'optimization_potential': await self._calculate_optimization_potential(idle_analysis),
                'recommended_actions': await self._generate_yield_recommendations(idle_analysis)
            }
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error analyzing idle assets: {e}")
            return {'error': str(e)}

    async def _is_asset_idle(self, currency: str, balance: Decimal) -> bool:
        """Determine if an asset is idle and available for yield optimization"""
        try:
            # Check minimum thresholds
            min_threshold = self.config.get('min_yield_threshold', {}).get(currency, 10.0)  # $10 default
            usd_value = await self._get_usd_value(currency, balance)
            
            if usd_value < min_threshold:
                return False
            
            # Check if asset is in active trading positions
            # This would integrate with the trading engine to check active orders
            # For now, assume assets above threshold are idle
            
            # Check if asset is already in yield positions
            if currency in self.active_positions:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error checking if {currency} is idle: {e}")
            return False

    async def _find_yield_opportunities(self, currency: str, balance: Decimal) -> List[YieldOpportunity]:
        """Find available yield opportunities for a specific currency"""
        try:
            opportunities = []
            
            # Check cache first
            cache_key = f"{currency}_{balance}"
            if cache_key in self.opportunity_cache:
                cached_data = self.opportunity_cache[cache_key]
                if time.time() - cached_data['timestamp'] < 300:  # 5 minutes cache
                    return cached_data['opportunities']
            
            # Strategy 1: Exchange staking
            staking_ops = await self._find_staking_opportunities(currency, balance)
            opportunities.extend(staking_ops)
            
            # Strategy 2: Lending opportunities
            lending_ops = await self._find_lending_opportunities(currency, balance)
            opportunities.extend(lending_ops)
            
            # Strategy 3: Liquidity mining
            liquidity_ops = await self._find_liquidity_mining_opportunities(currency, balance)
            opportunities.extend(liquidity_ops)
            
            # Strategy 4: Savings accounts
            savings_ops = await self._find_savings_opportunities(currency, balance)
            opportunities.extend(savings_ops)
            
            # Strategy 5: Grid trading for yield
            grid_ops = await self._find_grid_trading_opportunities(currency, balance)
            opportunities.extend(grid_ops)
            
            # Cache results
            self.opportunity_cache[cache_key] = {
                'opportunities': opportunities,
                'timestamp': time.time()
            }
            
            return opportunities
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error finding opportunities for {currency}: {e}")
            return []

    async def _find_staking_opportunities(self, currency: str, balance: Decimal) -> List[YieldOpportunity]:
        """Find staking opportunities for the currency"""
        try:
            opportunities = []
            
            # Define staking opportunities by currency
            staking_data = {
                'ETH': {'apy': 0.045, 'min_amount': 0.01, 'lock_days': 0, 'risk': 'low'},
                'SOL': {'apy': 0.065, 'min_amount': 0.1, 'lock_days': 3, 'risk': 'medium'},
                'ADA': {'apy': 0.055, 'min_amount': 10, 'lock_days': 5, 'risk': 'low'},
                'DOT': {'apy': 0.12, 'min_amount': 1, 'lock_days': 28, 'risk': 'medium'},
                'AVAX': {'apy': 0.085, 'min_amount': 0.5, 'lock_days': 14, 'risk': 'medium'},
                'ATOM': {'apy': 0.15, 'min_amount': 1, 'lock_days': 21, 'risk': 'medium'},
            }
            
            if currency in staking_data:
                data = staking_data[currency]
                
                if balance >= Decimal(str(data['min_amount'])):
                    opportunity = YieldOpportunity(
                        strategy=YieldStrategy.STAKING,
                        currency=currency,
                        platform='exchange_staking',
                        apy=data['apy'],
                        minimum_amount=Decimal(str(data['min_amount'])),
                        lock_period_days=data['lock_days'],
                        risk_level=data['risk'],
                        liquidity_score=0.8 if data['lock_days'] == 0 else 0.6,
                        auto_compound=True,
                        estimated_daily_yield=balance * Decimal(str(data['apy'])) / 365,
                        fees={'entry': 0.0, 'exit': 0.0, 'management': 0.001},
                        requirements=['minimum_balance'],
                        confidence=0.9
                    )
                    opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error finding staking opportunities: {e}")
            return []

    async def _find_lending_opportunities(self, currency: str, balance: Decimal) -> List[YieldOpportunity]:
        """Find lending opportunities for the currency"""
        try:
            opportunities = []
            
            # Define lending opportunities
            lending_data = {
                'USDT': {'apy': 0.08, 'min_amount': 10, 'lock_days': 0, 'risk': 'low'},
                'USDC': {'apy': 0.075, 'min_amount': 10, 'lock_days': 0, 'risk': 'low'},
                'BTC': {'apy': 0.035, 'min_amount': 0.001, 'lock_days': 0, 'risk': 'low'},
                'ETH': {'apy': 0.04, 'min_amount': 0.01, 'lock_days': 0, 'risk': 'low'},
            }
            
            if currency in lending_data:
                data = lending_data[currency]
                
                if balance >= Decimal(str(data['min_amount'])):
                    opportunity = YieldOpportunity(
                        strategy=YieldStrategy.LENDING,
                        currency=currency,
                        platform='exchange_lending',
                        apy=data['apy'],
                        minimum_amount=Decimal(str(data['min_amount'])),
                        lock_period_days=data['lock_days'],
                        risk_level=data['risk'],
                        liquidity_score=0.9,  # Usually high liquidity
                        auto_compound=True,
                        estimated_daily_yield=balance * Decimal(str(data['apy'])) / 365,
                        fees={'entry': 0.0, 'exit': 0.0, 'management': 0.0},
                        requirements=['kyc_verified'],
                        confidence=0.85
                    )
                    opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error finding lending opportunities: {e}")
            return []

    async def _find_liquidity_mining_opportunities(self, currency: str, balance: Decimal) -> List[YieldOpportunity]:
        """Find liquidity mining opportunities"""
        try:
            opportunities = []
            
            # Define liquidity mining pairs and rewards
            liquidity_pairs = {
                'BTC': [
                    {'pair': 'BTC/USDT', 'apy': 0.15, 'min_amount': 0.001, 'risk': 'medium'},
                    {'pair': 'BTC/ETH', 'apy': 0.12, 'min_amount': 0.001, 'risk': 'medium'}
                ],
                'ETH': [
                    {'pair': 'ETH/USDT', 'apy': 0.18, 'min_amount': 0.01, 'risk': 'medium'},
                    {'pair': 'ETH/BTC', 'apy': 0.14, 'min_amount': 0.01, 'risk': 'medium'}
                ],
                'SOL': [
                    {'pair': 'SOL/USDT', 'apy': 0.25, 'min_amount': 0.1, 'risk': 'high'},
                ]
            }
            
            if currency in liquidity_pairs:
                for pair_data in liquidity_pairs[currency]:
                    if balance >= Decimal(str(pair_data['min_amount'])):
                        opportunity = YieldOpportunity(
                            strategy=YieldStrategy.LIQUIDITY_MINING,
                            currency=currency,
                            platform='dex_liquidity',
                            apy=pair_data['apy'],
                            minimum_amount=Decimal(str(pair_data['min_amount'])),
                            lock_period_days=0,
                            risk_level=pair_data['risk'],
                            liquidity_score=0.7,
                            auto_compound=False,
                            estimated_daily_yield=balance * Decimal(str(pair_data['apy'])) / 365,
                            fees={'entry': 0.003, 'exit': 0.003, 'management': 0.0},
                            requirements=['impermanent_loss_risk'],
                            confidence=0.7
                        )
                        opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error finding liquidity mining opportunities: {e}")
            return []

    async def _find_savings_opportunities(self, currency: str, balance: Decimal) -> List[YieldOpportunity]:
        """Find savings account opportunities"""
        try:
            opportunities = []
            
            # Exchange savings products
            savings_data = {
                'USDT': {'apy': 0.05, 'min_amount': 1, 'lock_days': 0, 'risk': 'low'},
                'USDC': {'apy': 0.045, 'min_amount': 1, 'lock_days': 0, 'risk': 'low'},
                'BTC': {'apy': 0.02, 'min_amount': 0.0001, 'lock_days': 0, 'risk': 'low'},
                'ETH': {'apy': 0.025, 'min_amount': 0.001, 'lock_days': 0, 'risk': 'low'},
            }
            
            if currency in savings_data:
                data = savings_data[currency]
                
                if balance >= Decimal(str(data['min_amount'])):
                    opportunity = YieldOpportunity(
                        strategy=YieldStrategy.SAVINGS_ACCOUNT,
                        currency=currency,
                        platform='exchange_savings',
                        apy=data['apy'],
                        minimum_amount=Decimal(str(data['min_amount'])),
                        lock_period_days=data['lock_days'],
                        risk_level=data['risk'],
                        liquidity_score=1.0,  # Instant withdrawal
                        auto_compound=True,
                        estimated_daily_yield=balance * Decimal(str(data['apy'])) / 365,
                        fees={'entry': 0.0, 'exit': 0.0, 'management': 0.0},
                        requirements=['minimum_balance'],
                        confidence=0.95
                    )
                    opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error finding savings opportunities: {e}")
            return []

    async def _find_grid_trading_opportunities(self, currency: str, balance: Decimal) -> List[YieldOpportunity]:
        """Find grid trading opportunities for yield generation"""
        try:
            opportunities = []
            
            # Grid trading works best with volatile but range-bound assets
            grid_suitable = {
                'BTC': {'apy': 0.10, 'min_amount': 0.001, 'volatility': 'medium'},
                'ETH': {'apy': 0.12, 'min_amount': 0.01, 'volatility': 'medium'},
                'SOL': {'apy': 0.18, 'min_amount': 0.1, 'volatility': 'high'},
                'ADA': {'apy': 0.15, 'min_amount': 10, 'volatility': 'medium'},
            }
            
            if currency in grid_suitable:
                data = grid_suitable[currency]
                
                if balance >= Decimal(str(data['min_amount'])):
                    opportunity = YieldOpportunity(
                        strategy=YieldStrategy.GRID_TRADING,
                        currency=currency,
                        platform='exchange_grid',
                        apy=data['apy'],
                        minimum_amount=Decimal(str(data['min_amount'])),
                        lock_period_days=0,
                        risk_level='medium',
                        liquidity_score=0.8,
                        auto_compound=True,
                        estimated_daily_yield=balance * Decimal(str(data['apy'])) / 365,
                        fees={'entry': 0.001, 'exit': 0.001, 'trading': 0.001},
                        requirements=['market_volatility'],
                        confidence=0.75
                    )
                    opportunities.append(opportunity)
            
            return opportunities
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error finding grid trading opportunities: {e}")
            return []

    def _calculate_opportunity_score(self, opportunities: List[YieldOpportunity]) -> float:
        """Calculate overall opportunity score for a currency"""
        try:
            if not opportunities:
                return 0.0
            
            # Weight factors
            weights = {
                'apy': 0.4,
                'liquidity': 0.2,
                'risk': 0.2,
                'confidence': 0.2
            }
            
            scores = []
            for opp in opportunities:
                # Normalize APY (assume max 50% APY)
                apy_score = min(1.0, opp.apy / 0.5)
                
                # Liquidity score (already 0-1)
                liquidity_score = opp.liquidity_score
                
                # Risk score (invert - lower risk is better)
                risk_scores = {'low': 1.0, 'medium': 0.7, 'high': 0.4}
                risk_score = risk_scores.get(opp.risk_level, 0.5)
                
                # Confidence score (already 0-1)
                confidence_score = opp.confidence
                
                # Calculate weighted score
                weighted_score = (
                    apy_score * weights['apy'] +
                    liquidity_score * weights['liquidity'] +
                    risk_score * weights['risk'] +
                    confidence_score * weights['confidence']
                )
                
                scores.append(weighted_score)
            
            # Return best opportunity score
            return max(scores) if scores else 0.0
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error calculating opportunity score: {e}")
            return 0.0

    def _get_recommended_strategy(self, opportunities: List[YieldOpportunity]) -> Optional[YieldOpportunity]:
        """Get the recommended yield strategy from available opportunities"""
        try:
            if not opportunities:
                return None
            
            # Score each opportunity
            scored_opportunities = []
            for opp in opportunities:
                score = self._calculate_single_opportunity_score(opp)
                scored_opportunities.append((score, opp))
            
            # Sort by score (highest first)
            scored_opportunities.sort(key=lambda x: x[0], reverse=True)
            
            return scored_opportunities[0][1] if scored_opportunities else None
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error getting recommended strategy: {e}")
            return None

    def _calculate_single_opportunity_score(self, opportunity: YieldOpportunity) -> float:
        """Calculate score for a single opportunity"""
        try:
            # Base score from APY
            score = opportunity.apy * 100  # Convert to percentage points
            
            # Adjust for risk
            risk_multipliers = {'low': 1.0, 'medium': 0.8, 'high': 0.6}
            score *= risk_multipliers.get(opportunity.risk_level, 0.7)
            
            # Adjust for liquidity
            score *= opportunity.liquidity_score
            
            # Adjust for confidence
            score *= opportunity.confidence
            
            # Penalty for lock periods
            if opportunity.lock_period_days > 0:
                lock_penalty = 1.0 - (opportunity.lock_period_days / 365) * 0.2  # 20% penalty per year
                score *= max(0.5, lock_penalty)
            
            return score
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error calculating single opportunity score: {e}")
            return 0.0

    async def _get_usd_value(self, currency: str, amount: Decimal) -> Decimal:
        """Get USD value of a currency amount"""
        try:
            if currency in ['USD', 'USDT', 'USDC']:
                return amount
            
            # Try to get price from exchange clients
            for client in self.exchange_clients.values():
                if hasattr(client, 'get_price'):
                    symbol = f"{currency}USDT"
                    price = client.get_price(symbol)
                    if price and float(price) > 0:
                        return amount * Decimal(str(price))
            
            return Decimal('0')
            
        except Exception as e:
            logger.error(f"❌ [YIELD] Error getting USD value for {currency}: {e}")
            return Decimal('0')

# Export the main class
__all__ = ['AdvancedYieldOptimizer', 'YieldOpportunity', 'YieldPosition', 'YieldStrategy']
