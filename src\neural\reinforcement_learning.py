"""
Reinforcement Learning Agent for Neural Strategy Components
Provides ReinforcementLearningAgent class for strategy optimization
"""

import logging
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from typing import Dict, List, Optional, Any, Tuple
from collections import deque
import random

logger = logging.getLogger(__name__)

class ReinforcementLearningAgent:
    """
    Reinforcement Learning Agent for strategy optimization and selection
    Compatible with adaptive_neural_strategy and neural_strategy_manager
    """
    
    def __init__(self, state_size: int, action_size: int, learning_rate: float = 0.001, **kwargs):
        """
        Initialize RL Agent
        
        Args:
            state_size: Size of the state space (market features)
            action_size: Size of the action space (strategies or parameters)
            learning_rate: Learning rate for neural network
        """
        self.state_size = state_size
        self.action_size = action_size
        self.learning_rate = learning_rate
        
        # Hyperparameters
        self.epsilon = kwargs.get('epsilon', 0.1)  # Exploration rate
        self.epsilon_decay = kwargs.get('epsilon_decay', 0.995)
        self.epsilon_min = kwargs.get('epsilon_min', 0.01)
        self.gamma = kwargs.get('gamma', 0.95)  # Discount factor
        self.batch_size = kwargs.get('batch_size', 32)
        self.memory_size = kwargs.get('memory_size', 10000)
        
        # Experience replay memory
        self.memory = deque(maxlen=self.memory_size)
        
        # Neural network setup
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self._build_model()
        
        # Training metrics
        self.training_step = 0
        self.last_loss = 0.0
        
        logger.info(f"ReinforcementLearningAgent initialized: state_size={state_size}, action_size={action_size}")
    
    def _build_model(self):
        """Build the neural network model"""
        class DQN(nn.Module):
            def __init__(self, state_size, action_size):
                super(DQN, self).__init__()
                self.fc1 = nn.Linear(state_size, 128)
                self.fc2 = nn.Linear(128, 128)
                self.fc3 = nn.Linear(128, 64)
                self.fc4 = nn.Linear(64, action_size)
                self.dropout = nn.Dropout(0.2)
                
            def forward(self, x):
                x = torch.relu(self.fc1(x))
                x = self.dropout(x)
                x = torch.relu(self.fc2(x))
                x = self.dropout(x)
                x = torch.relu(self.fc3(x))
                x = self.fc4(x)
                return x
        
        self.q_network = DQN(self.state_size, self.action_size).to(self.device)
        self.target_network = DQN(self.state_size, self.action_size).to(self.device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=self.learning_rate)
        
        # Initialize target network with same weights
        self.update_target_network()
    
    def update_target_network(self):
        """Update target network with current network weights"""
        self.target_network.load_state_dict(self.q_network.state_dict())
    
    def remember(self, state, action, reward, next_state, done):
        """Store experience in replay memory"""
        self.memory.append((state, action, reward, next_state, done))
    
    def predict(self, state):
        """Predict action probabilities for given state"""
        try:
            if isinstance(state, (list, np.ndarray)):
                state = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            elif isinstance(state, torch.Tensor):
                if state.dim() == 1:
                    state = state.unsqueeze(0)
                state = state.to(self.device)
            
            self.q_network.eval()
            with torch.no_grad():
                q_values = self.q_network(state)
                # Convert Q-values to probabilities using softmax
                probabilities = torch.softmax(q_values, dim=1)
                return probabilities.cpu().numpy().flatten()
                
        except Exception as e:
            logger.error(f"Error in predict: {e}")
            # Return uniform probabilities as fallback
            return np.ones(self.action_size) / self.action_size
    
    def act(self, state):
        """Choose action based on epsilon-greedy policy"""
        if np.random.random() <= self.epsilon:
            return random.randrange(self.action_size)
        
        action_probs = self.predict(state)
        return np.argmax(action_probs)
    
    def replay(self, batch_size=None):
        """Train the model on a batch of experiences"""
        if batch_size is None:
            batch_size = self.batch_size
            
        if len(self.memory) < batch_size:
            return
        
        try:
            # Sample batch from memory
            batch = random.sample(self.memory, batch_size)
            states, actions, rewards, next_states, dones = zip(*batch)
            
            # Convert to tensors
            states = torch.FloatTensor(states).to(self.device)
            actions = torch.LongTensor(actions).to(self.device)
            rewards = torch.FloatTensor(rewards).to(self.device)
            next_states = torch.FloatTensor(next_states).to(self.device)
            dones = torch.BoolTensor(dones).to(self.device)
            
            # Current Q values
            current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
            
            # Next Q values from target network
            next_q_values = self.target_network(next_states).max(1)[0].detach()
            target_q_values = rewards + (self.gamma * next_q_values * ~dones)
            
            # Compute loss
            loss = nn.MSELoss()(current_q_values.squeeze(), target_q_values)
            
            # Optimize
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()
            
            # Update epsilon
            if self.epsilon > self.epsilon_min:
                self.epsilon *= self.epsilon_decay
            
            # Update training metrics
            self.training_step += 1
            self.last_loss = loss.item()
            
            # Update target network periodically
            if self.training_step % 100 == 0:
                self.update_target_network()
            
            logger.debug(f"RL training step {self.training_step}: loss={self.last_loss:.4f}, epsilon={self.epsilon:.4f}")
            
        except Exception as e:
            logger.error(f"Error in replay training: {e}")
    
    def save_model(self, filepath: str):
        """Save the trained model"""
        try:
            torch.save({
                'q_network_state_dict': self.q_network.state_dict(),
                'target_network_state_dict': self.target_network.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'epsilon': self.epsilon,
                'training_step': self.training_step
            }, filepath)
            logger.info(f"RL model saved to {filepath}")
        except Exception as e:
            logger.error(f"Error saving RL model: {e}")
    
    def load_model(self, filepath: str):
        """Load a trained model"""
        try:
            checkpoint = torch.load(filepath, map_location=self.device)
            self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
            self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.epsilon = checkpoint.get('epsilon', self.epsilon)
            self.training_step = checkpoint.get('training_step', 0)
            logger.info(f"RL model loaded from {filepath}")
        except Exception as e:
            logger.error(f"Error loading RL model: {e}")
    
    def get_training_stats(self) -> Dict[str, Any]:
        """Get current training statistics"""
        return {
            'training_step': self.training_step,
            'epsilon': self.epsilon,
            'last_loss': self.last_loss,
            'memory_size': len(self.memory),
            'device': str(self.device)
        }

    def calculate_enhanced_reward(self, trade_result: Dict[str, Any],
                                market_data: Dict[str, Any],
                                sentiment_data: Optional[Dict[str, Any]] = None) -> float:
        """
        Calculate enhanced reward with sentiment-based scoring and Fear & Greed Index factors

        Args:
            trade_result: Dictionary containing trade outcome (profit/loss, fees, etc.)
            market_data: Current market data including Fear & Greed Index
            sentiment_data: Sentiment analysis data

        Returns:
            Enhanced reward score incorporating sentiment factors
        """
        try:
            # Base reward from trade performance
            base_reward = self._calculate_base_reward(trade_result)

            # Sentiment-based reward adjustments
            sentiment_reward = self._calculate_sentiment_reward(market_data, sentiment_data)

            # Fear & Greed Index reward factors
            fear_greed_reward = self._calculate_fear_greed_reward(market_data)

            # Market timing reward (reward good timing in different market regimes)
            timing_reward = self._calculate_timing_reward(trade_result, market_data)

            # Risk-adjusted reward (penalize high-risk trades in volatile conditions)
            risk_adjustment = self._calculate_risk_adjustment(trade_result, market_data)

            # Combine all reward components with weights
            total_reward = (
                base_reward * 0.5 +           # 50% base performance
                sentiment_reward * 0.2 +      # 20% sentiment factors
                fear_greed_reward * 0.15 +    # 15% Fear & Greed factors
                timing_reward * 0.1 +         # 10% market timing
                risk_adjustment * 0.05        # 5% risk adjustment
            )

            # Apply bounds to prevent extreme rewards
            total_reward = max(-2.0, min(2.0, total_reward))

            logger.debug(f"Enhanced reward calculation: base={base_reward:.3f}, "
                        f"sentiment={sentiment_reward:.3f}, fear_greed={fear_greed_reward:.3f}, "
                        f"timing={timing_reward:.3f}, risk_adj={risk_adjustment:.3f}, "
                        f"total={total_reward:.3f}")

            return total_reward

        except Exception as e:
            logger.error(f"Error calculating enhanced reward: {e}")
            return 0.0  # Neutral reward on error

    def _calculate_base_reward(self, trade_result: Dict[str, Any]) -> float:
        """Calculate base reward from trade performance"""
        try:
            profit_loss = trade_result.get('profit_loss', 0.0)
            fees = trade_result.get('fees', 0.0)

            # Net profit after fees
            net_profit = profit_loss - fees

            # Normalize by trade size for percentage-based reward
            trade_size = trade_result.get('trade_size', 1.0)
            if trade_size > 0:
                profit_percentage = net_profit / trade_size
            else:
                profit_percentage = 0.0

            # Scale reward: +1 for 1% profit, -1 for 1% loss
            base_reward = profit_percentage * 100

            # Bonus for profitable trades
            if net_profit > 0:
                base_reward *= 1.1  # 10% bonus for profitable trades

            return base_reward

        except Exception as e:
            logger.error(f"Error calculating base reward: {e}")
            return 0.0

    def _calculate_sentiment_reward(self, market_data: Dict[str, Any],
                                  sentiment_data: Optional[Dict[str, Any]]) -> float:
        """Calculate sentiment-based reward adjustments"""
        try:
            reward = 0.0

            if sentiment_data:
                sentiment_score = sentiment_data.get('aggregated_sentiment', 0.0)
                confidence = sentiment_data.get('confidence', 0.0)

                # Reward alignment with strong sentiment signals
                sentiment_strength = abs(sentiment_score) * confidence
                reward += sentiment_strength * 0.5

            # Web crawler sentiment insights
            web_insights = market_data.get('web_crawler_insights', {})
            if web_insights:
                web_sentiment = web_insights.get('overall_sentiment', 0.0)
                high_impact_count = web_insights.get('high_impact_count', 0)

                # Reward for acting on high-impact news
                if high_impact_count > 0:
                    reward += min(0.3, high_impact_count * 0.1)

                # Reward for sentiment alignment
                reward += abs(web_sentiment) * 0.2

            return reward

        except Exception as e:
            logger.error(f"Error calculating sentiment reward: {e}")
            return 0.0

    def _calculate_fear_greed_reward(self, market_data: Dict[str, Any]) -> float:
        """Calculate Fear & Greed Index reward factors"""
        try:
            live_aggregated = market_data.get('live_aggregated', {})
            fear_greed_index = live_aggregated.get('fear_greed_index', 65.0)

            # Reward contrarian trades in extreme conditions
            if fear_greed_index <= 25:  # Extreme Fear
                # Reward buying in extreme fear (contrarian)
                return 0.4
            elif fear_greed_index >= 75:  # Extreme Greed
                # Reward selling in extreme greed (contrarian)
                return 0.4
            elif 45 <= fear_greed_index <= 55:  # Neutral zone
                # Small reward for trading in neutral conditions
                return 0.1
            else:
                # Moderate conditions
                return 0.2

        except Exception as e:
            logger.error(f"Error calculating Fear & Greed reward: {e}")
            return 0.0

    def _calculate_timing_reward(self, trade_result: Dict[str, Any],
                               market_data: Dict[str, Any]) -> float:
        """Calculate market timing reward"""
        try:
            # Reward good timing based on market volatility and volume
            price_data = market_data.get('price_data', {})
            if not price_data:
                return 0.0

            # Calculate average market volatility
            volatilities = [
                float(data.get('volatility', 0.1))
                for data in price_data.values()
                if 'volatility' in data
            ]

            if volatilities:
                avg_volatility = np.mean(volatilities)

                # Reward trading in optimal volatility conditions
                if 0.02 <= avg_volatility <= 0.08:  # Optimal volatility range
                    return 0.3
                elif avg_volatility > 0.15:  # High volatility - risky
                    return -0.2
                else:
                    return 0.1

            return 0.0

        except Exception as e:
            logger.error(f"Error calculating timing reward: {e}")
            return 0.0

    def _calculate_risk_adjustment(self, trade_result: Dict[str, Any],
                                 market_data: Dict[str, Any]) -> float:
        """Calculate risk adjustment factor"""
        try:
            # Penalize high-risk trades in volatile conditions
            trade_size = trade_result.get('trade_size', 0.0)
            account_balance = trade_result.get('account_balance', 1.0)

            if account_balance > 0:
                position_size_ratio = trade_size / account_balance

                # Penalize oversized positions
                if position_size_ratio > 0.9:  # >90% of balance
                    return -0.5
                elif position_size_ratio > 0.7:  # >70% of balance
                    return -0.3
                elif position_size_ratio < 0.1:  # <10% of balance (too conservative)
                    return -0.1
                else:
                    return 0.1  # Good position sizing

            return 0.0

        except Exception as e:
            logger.error(f"Error calculating risk adjustment: {e}")
            return 0.0

    async def update_with_reward(self, reward_data: Dict[str, Any]):
        """Update the RL agent with reward data - MISSING METHOD FIXED"""
        try:
            # Extract reward and state information
            reward = reward_data.get('reward', 0.0)

            # Create a simple state representation from the reward data
            state_features = []

            # Add available features to state
            if 'symbol' in reward_data:
                # Simple hash of symbol for state representation
                state_features.append(hash(reward_data['symbol']) % 100 / 100.0)
            else:
                state_features.append(0.5)  # Default

            if 'strategy' in reward_data:
                # Simple hash of strategy for state representation
                state_features.append(hash(str(reward_data['strategy'])) % 100 / 100.0)
            else:
                state_features.append(0.5)  # Default

            # Add numerical features
            for key in ['duration', 'confidence', 'efficiency_score', 'funding_rate']:
                if key in reward_data:
                    # Normalize the value
                    value = float(reward_data[key])
                    normalized_value = max(0.0, min(1.0, value / 100.0))
                    state_features.append(normalized_value)
                else:
                    state_features.append(0.5)  # Default

            # Pad or truncate to match state_size
            while len(state_features) < self.state_size:
                state_features.append(0.5)  # Default padding
            state_features = state_features[:self.state_size]

            # Convert to numpy array
            state = np.array(state_features, dtype=np.float32)

            # Choose a random action for this update (simplified)
            action = random.randrange(self.action_size)

            # Create next state (same as current for simplicity)
            next_state = state.copy()

            # Store experience
            self.remember(state, action, reward, next_state, done=True)

            # Train if we have enough experiences
            if len(self.memory) >= self.batch_size:
                self.replay()

            logger.debug(f"RL agent updated with reward: {reward:.3f}")

        except Exception as e:
            logger.error(f"Error in update_with_reward: {e}")

    async def update_q_values(self, state: Dict[str, Any], action: str, reward: float):
        """Update Q-values with state-action-reward data"""
        try:
            # Convert state dict to array
            state_features = []

            # Extract numerical features from state
            for key in ['funding_rate', 'volatility', 'time_factor', 'confidence']:
                if key in state:
                    value = float(state[key])
                    # Normalize to [0, 1] range
                    if key == 'funding_rate':
                        normalized_value = max(0.0, min(1.0, (value + 0.01) / 0.02))  # Assume range [-0.01, 0.01]
                    elif key == 'volatility':
                        normalized_value = max(0.0, min(1.0, value / 0.1))  # Assume max 10% volatility
                    else:
                        normalized_value = max(0.0, min(1.0, value))
                    state_features.append(normalized_value)
                else:
                    state_features.append(0.5)  # Default

            # Pad or truncate to match state_size
            while len(state_features) < self.state_size:
                state_features.append(0.5)
            state_features = state_features[:self.state_size]

            # Convert to numpy array
            state_array = np.array(state_features, dtype=np.float32)

            # Convert action string to index
            action_index = hash(action) % self.action_size

            # Create next state (same as current for simplicity)
            next_state = state_array.copy()

            # Store experience
            self.remember(state_array, action_index, reward, next_state, done=True)

            # Train if we have enough experiences
            if len(self.memory) >= self.batch_size:
                self.replay()

            logger.debug(f"Q-values updated: action={action}, reward={reward:.3f}")

        except Exception as e:
            logger.error(f"Error updating Q-values: {e}")
